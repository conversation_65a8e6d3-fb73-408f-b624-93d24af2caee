package com.gianghp.hrm_hr_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_hr_service.dtos.DepartmentDto;
import com.gianghp.hrm_hr_service.dtos.EmployeeDto;
import com.gianghp.hrm_hr_service.dtos.UpdateDepartmentDto;
import com.gianghp.hrm_hr_service.services.DepartmentService;
import com.gianghp.hrm_hr_service.services.EmployeeService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * Employee Controller - <PERSON><PERSON> dụ về cách sử dụng JWT validation
 */
@RestController
@RequestMapping("/departments")
@RequiredArgsConstructor
public class DepartmentController {

    private final DepartmentService departmentService;

    @GetMapping
    public ResponseEntity<?> getAllDepartment(
    ) {
        try {
            List<DepartmentDto> departments = departmentService.getAllDepartments();
            return ResponseEntity.ok(ApiResponse.success("Departments retrieved successfully", departments));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve departments: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getDepartmentById(@PathVariable UUID id) {
        try {
            DepartmentDto department = departmentService.getDepartmentById(id);
            return ResponseEntity.ok(ApiResponse.success("Department retrieved successfully", department));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve department: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}/employees")
    public ResponseEntity<?> getEmployeesByDepartment(@PathVariable UUID id,
                                                      @RequestParam(defaultValue = "0") int page,
                                                      @RequestParam(defaultValue = "10") int size,
                                                      @RequestParam(defaultValue = "createdAt") String sortBy,
                                                      @RequestParam(defaultValue = "desc") String sortDir
    ) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc")
                    ? Sort.by(sortBy).descending()
                    : Sort.by(sortBy).ascending();

            Pageable pageable = PageRequest.of(page, size, sort);
            Page<EmployeeDto> employees = departmentService.getEmployeesByDepartment(id, pageable);
            ApiResponse<List<EmployeeDto>> response = ApiResponse.success(
                    "Employees retrieved successfully",
                    employees.getContent(),
                    employees.getTotalElements(),
                    employees.getTotalPages(),
                    employees.getNumber()
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve employees: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}/all-employees")
    public ResponseEntity<ApiResponse<List<EmployeeDto>>> getAllEmployeesByDepartment(@PathVariable UUID id
    ) {
        try {
                List<EmployeeDto> employees = departmentService.getAllEmployeesByDepartment(id);
            ApiResponse<List<EmployeeDto>> response = ApiResponse.success(
                    "Employees retrieved successfully", employees
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve employees: " + e.getMessage()));
        }
    }


    @GetMapping("/manager/employees")
    public ResponseEntity<?> getEmployeesByMyDepartment(
        @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "createdAt") String sortBy,
        @RequestParam(defaultValue = "desc") String sortDir
            ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            Sort sort = sortDir.equalsIgnoreCase("desc")
                    ? Sort.by(sortBy).descending()
                    : Sort.by(sortBy).ascending();

            Pageable pageable = PageRequest.of(page, size, sort);
            Page<EmployeeDto> employees = departmentService.getEmployeesByMyDepartment(userId, pageable);
            ApiResponse<List<EmployeeDto>> response = ApiResponse.success(
                    "Employees retrieved successfully",
                    employees.getContent(),
                    employees.getTotalElements(),
                    employees.getTotalPages(),
                    employees.getNumber()
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve employees: " + e.getMessage()));
        }
    }

    @GetMapping("/manager/all-employees")
    public ResponseEntity<ApiResponse<List<EmployeeDto>>> getAllEmployeesByMyDepartment(
            @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            List<EmployeeDto> employees = departmentService.getAllEmployeesByMyDepartment(userId);
            ApiResponse<List<EmployeeDto>> response = ApiResponse.success(
                    "Employees retrieved successfully", employees
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve employees: " + e.getMessage()));
        }
    }

    @GetMapping("/manager")
    public ResponseEntity<ApiResponse<DepartmentDto>> getMyDepartment(
            @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            DepartmentDto department = departmentService.getMyDepartment(userId);
            return ResponseEntity.ok(ApiResponse.success("Department retrieved successfully", department));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve department: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}/manager")
    public ResponseEntity<?> getDepartmentManager(@PathVariable UUID id) {
        try {
            EmployeeDto manager = departmentService.getDepartmentManager(id);
            return ResponseEntity.ok(ApiResponse.success("Department manager retrieved successfully", manager));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve department manager: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<?> createDepartment(@RequestBody DepartmentDto departmentDto) {
        try {
            DepartmentDto createdDepartment = departmentService.createDepartment(departmentDto);
            return ResponseEntity.ok(ApiResponse.success("Department created successfully", createdDepartment));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to create department: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateDepartment(@PathVariable UUID id, @RequestBody UpdateDepartmentDto updateDepartmentDto) {
        try {
            DepartmentDto updatedDepartment = departmentService.updateDepartment(id, updateDepartmentDto);
            return ResponseEntity.ok(ApiResponse.success("Department updated successfully", updatedDepartment));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to update department: " + e.getMessage()));
        }
    }
}
