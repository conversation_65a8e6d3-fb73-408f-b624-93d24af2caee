spring.application.name=hrm-payroll-service
spring.config.import=classpath:application-common.properties
server.port=6040
security.config=common

# Database Configuration
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=ggmacket123
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update

# Kafka Configuration
spring.kafka.consumer.group-id=${spring.application.name}-group
