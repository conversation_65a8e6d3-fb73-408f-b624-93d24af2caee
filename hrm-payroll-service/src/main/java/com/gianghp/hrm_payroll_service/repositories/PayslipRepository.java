package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.Payslip;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PayslipRepository extends JpaRepository<Payslip, UUID> {

  boolean existsByEmployee_IdAndMonth(UUID employeeId, YearMonth yearMonth);

  Payslip findTopByEmployee_IdOrderByIssuedDateDesc(UUID employeeId);

  Payslip findByEmployee_IdAndIssuedDate(UUID employeeId, LocalDate issuedDate);

  Page<Payslip> findAllByEmployee_Id(UUID employeeId, Pageable pageable);
}
