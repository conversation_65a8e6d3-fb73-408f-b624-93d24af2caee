package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.PayrollSetting;
import java.time.LocalDate;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PayrollSettingRepository extends JpaRepository<PayrollSetting, UUID> {

  PayrollSetting findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate effectiveDate);
}
