package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.DailySalary;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DailySalaryRepository extends JpaRepository<DailySalary, UUID> {
  Page<DailySalary> findByAttendance_EmployeeCache_IdAndAttendance_DateBetween(UUID employeeId, LocalDate startDate, LocalDate endDate, Pageable pageable);

  List<DailySalary> findByAttendance_EmployeeCache_IdAndAttendance_DateBetween(UUID employeeId, LocalDate startDate, LocalDate endDate);

  Page<DailySalary> findByAttendance_DateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);

  List<DailySalary> findByAttendance_EmployeeCache_IdAndAttendance_DateBetweenOrderByAttendance_DateAsc(
      UUID id, LocalDate startDate, LocalDate endDate);
}
