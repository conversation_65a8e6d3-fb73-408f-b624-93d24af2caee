package com.gianghp.hrm_payroll_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_payroll_service.dtos.PayslipDto;
import com.gianghp.hrm_payroll_service.services.PayslipService;
import io.swagger.v3.oas.annotations.Parameter;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/payslips")
@RequiredArgsConstructor
public class PayslipController {

  private final PayslipService payslipService;

  @GetMapping("/employee/{employeeId}/generate/{yearMonth}")
  public ResponseEntity<ApiResponse<PayslipDto>> getPayslipByEmployee(
      @PathVariable UUID employeeId,
      @PathVariable YearMonth yearMonth
  ) {
    try {
      PayslipDto payslipDto = payslipService.generatePayslipForEmployee(employeeId, yearMonth);
      return ResponseEntity.ok(ApiResponse.success("Payslip retrieved successfully", payslipDto));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to retrieve payslip: " + e.getMessage()));
    }

  }

  @GetMapping("/me/current")
  public ResponseEntity<ApiResponse<PayslipDto>> getMyPayslip(
      @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
  ) {
    try {
      return ResponseEntity.ok(ApiResponse.success("Payslip retrieved successfully",
          payslipService.getCurrentPayslipForEmployee(UUID.fromString(userIdHeader))));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to retrieve payslip: " + e.getMessage()));
    }

  }

  @GetMapping("/me/{issuedDate}")
  public ResponseEntity<ApiResponse<PayslipDto>> getMyPayslipByIssuedDate(
      @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader,
      @PathVariable LocalDate issuedDate
  ) {
    try {
      return ResponseEntity.ok(ApiResponse.success("Payslip retrieved successfully",
          payslipService.getPayslipByIssuedDate(UUID.fromString(userIdHeader), issuedDate)));

    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to retrieve payslip: " + e.getMessage()));
    }
  }

  @GetMapping("/me/all")
  public ResponseEntity<ApiResponse<List<PayslipDto>>> getAllMyPayslip(
      @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "issuedDate") String sortBy,
      @RequestParam(defaultValue = "desc") String sortDir
  ) {
    try {
      Pageable pageable = PageRequest.of(page, size, sortDir.equalsIgnoreCase("desc")
          ? Sort.by(sortBy).descending()
          : Sort.by(sortBy).ascending());

      Page<PayslipDto> payslipDtos = payslipService.findAllByEmployeeId(
          UUID.fromString(userIdHeader), pageable);

      return ResponseEntity.ok(ApiResponse.success("Payslip retrieved successfully",
          payslipDtos.getContent(), payslipDtos.getTotalElements(), payslipDtos.getTotalPages(),
          payslipDtos.getNumber()
      ));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to retrieve payslip: " + e.getMessage()));
    }
  }

}
