package com.gianghp.hrm_payroll_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_payroll_service.dtos.DailySalaryDto;
import com.gianghp.hrm_payroll_service.dtos.WeeklySalaryDto;
import com.gianghp.hrm_payroll_service.services.DailySalaryService;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/daily-salaries")
@RequiredArgsConstructor
public class DailySalaryController {

  private final DailySalaryService dailySalaryService;

  @GetMapping
  public ResponseEntity<ApiResponse<List<DailySalaryDto>>> getAllDailySalary(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "attendance.date") String sortBy,
      @RequestParam(defaultValue = "desc") String sortDir
  ) {
    try {
      Pageable pageable = PageRequest.of(page, size, sortDir.equalsIgnoreCase("desc")
          ? Sort.by(sortBy).descending()
          : Sort.by(sortBy).ascending());

      Page<DailySalaryDto> dailySalary = dailySalaryService.findAll(pageable);
      return ResponseEntity.ok(ApiResponse.success("Daily salary retrieved successfully",
          dailySalary.getContent(), dailySalary.getTotalElements(), dailySalary.getTotalPages(),
          dailySalary.getNumber()
      ));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to retrieve daily salary: " + e.getMessage()));
    }
  }

  @GetMapping("/weekly/{startDate}/employee/{employeeId}")
  public ResponseEntity<ApiResponse<List<DailySalaryDto>>> getWeeklySalary(
      @PathVariable LocalDate startDate,
      @PathVariable UUID employeeId
  ) {
    try {
      return ResponseEntity.ok(ApiResponse.success("Daily salary calculated successfully",
          dailySalaryService.getByEmployeeIdAndWeeklySalary(employeeId, startDate)));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to calculate daily salary: " + e.getMessage()));
    }
  }

  @GetMapping("/weekly/{localDate}")
  public ResponseEntity<ApiResponse<List<WeeklySalaryDto>>> getWeeklySalary(
      @PathVariable LocalDate localDate,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "employeeCode") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir
  ) {
    try {
      Pageable pageable = PageRequest.of(page, size, sortDir.equalsIgnoreCase("desc")
          ? Sort.by(sortBy).descending()
          : Sort.by(sortBy).ascending());

      Page<WeeklySalaryDto> weeklySalaryDtos = dailySalaryService.getWeekylySalary(localDate,
          pageable);

      return ResponseEntity.ok(
          ApiResponse.success("Weekly salary retrieved successfully", weeklySalaryDtos.getContent(),
              weeklySalaryDtos.getTotalElements(), weeklySalaryDtos.getTotalPages(),
              weeklySalaryDtos.getNumber()));

    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to retrieve weekly salary: " + e.getMessage()));
    }
  }

  @GetMapping("/calculate/{localDate}")
  public ResponseEntity<ApiResponse<String>> calculateDailySalaryForAllEmployees(
      @PathVariable LocalDate localDate
  ) {
    try {
      dailySalaryService.caculateDailySalaryForAllEmployees(localDate);
      return ResponseEntity.ok(ApiResponse.success("Daily salary calculated successfully", null));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to calculate daily salary: " + e.getMessage()));
    }
  }

}
