package com.gianghp.hrm_payroll_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_payroll_service.dtos.TaxDto;
import com.gianghp.hrm_payroll_service.services.TaxService;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/taxes")
@RequiredArgsConstructor
public class TaxController {
  private final TaxService taxService;

  @GetMapping
  public ResponseEntity<ApiResponse<List<TaxDto>>> getCurrentTaxes() {
    try {
      return ResponseEntity.ok(ApiResponse.success("Taxes retrieved successfully", taxService.getCurrentTaxes()));
    } catch (Exception e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve taxes: " + e.getMessage()));
    }
  }
}
