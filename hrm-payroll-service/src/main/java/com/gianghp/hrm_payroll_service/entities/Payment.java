package com.gianghp.hrm_payroll_service.entities;


import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.PaymentStatus;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "payment")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Payment extends BaseEntity {

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "payslip_id", nullable = false)
  private Payslip payslip; // Phiếu lương được thanh toán

  @Column(name = "transaction_ref", length = 100)
  private String transactionRef; // Mã giao dịch ngân hàng

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false, length = 20)
  private PaymentStatus status; // SUCCESS, FAILED, PENDING

  @Column(name = "payment_date", nullable = false)
  private LocalDateTime paymentDate; // Ngày giờ thanh toán
}

