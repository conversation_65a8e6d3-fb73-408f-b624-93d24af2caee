package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.CurrencyCode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "payroll_setting")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PayrollSetting extends BaseEntity {

  @Column(name = "cut_off_day", nullable = false)
  private Integer cutOffDay;

  @Column(name = "pay_day", nullable = false)
  private Integer payDay;

  @Column(name = "effective_date")
  private LocalDate effectiveDate;

  @Column(name = "currency", nullable = false, length = 3)
  @Enumerated(EnumType.STRING)
  private CurrencyCode currency = CurrencyCode.VND; // default
}
