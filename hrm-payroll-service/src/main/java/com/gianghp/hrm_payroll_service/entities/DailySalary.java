package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.entities.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "daily_salary")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DailySalary extends BaseEntity {
    @OneToOne
    @JoinColumn(name = "attendance_id", nullable = false)
    private AttendanceForPayrollCache attendance;

    @Column(name = "gross_amount", nullable = false, precision = NumericConstants.PAYROLL_AMOUNT_PRECISION, scale = NumericConstants.PAYROLL_AMOUNT_SCALE)
    private BigDecimal grossAmount;

    @Column(name = "total_deduction", nullable = false, precision = NumericConstants.PAYROLL_AMOUNT_PRECISION, scale = NumericConstants.PAYROLL_AMOUNT_SCALE)
    private BigDecimal totalDeduction;

    @Column(name = "net_amount", nullable = false, precision = NumericConstants.PAYROLL_AMOUNT_PRECISION, scale = NumericConstants.PAYROLL_AMOUNT_SCALE)
    private BigDecimal netAmount;

    @Column(name = "total_tax", nullable = false, precision = NumericConstants.PAYROLL_AMOUNT_PRECISION, scale = NumericConstants.PAYROLL_AMOUNT_SCALE)
    private BigDecimal totalTax;

    @Column(name = "note")
    private String note;
}
