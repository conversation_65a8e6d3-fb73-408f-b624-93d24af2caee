package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.PayslipStatus;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Table(name = "payslip")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Payslip extends BaseEntity {

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "employee_id", nullable = false)
  private EmployeeCache employee; // Nhân viên nhận lương

  @Column(name = "gross_salary", nullable = false, precision = 18, scale = 2)
  private BigDecimal grossSalary; // Lương trước khi trừ

  @Column(name = "total_deduction", nullable = false, precision = 18, scale = 2)
  private BigDecimal totalDeduction; // Tổng khấu trừ (thuế, bảo hiểm...// )

  @Column(name = "total_tax", nullable = false, precision = 18, scale = 2)
  private BigDecimal totalTax; // Tổng thuế

  @Column(name = "net_salary", nullable = false, precision = 18, scale = 2)
  private BigDecimal netSalary; // Lương thực nhận

  @Column(name = "issued_date", nullable = false)
  private LocalDate issuedDate; // Ngày phát hành phiếu lương

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false, length = 20)
  private PayslipStatus status; // GENERATED, APPROVED, PAID

  @Column(name = "note")
  private String note;
}
