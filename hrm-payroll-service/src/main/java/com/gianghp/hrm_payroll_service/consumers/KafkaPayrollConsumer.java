package com.gianghp.hrm_payroll_service.consumers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.ApprovedOvertimeBasicDto;
import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm.dtos.AttendanceBasicWrapperDto;
import com.gianghp.hrm.dtos.AttendanceSettingBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import com.gianghp.hrm_payroll_service.acl.AttendanceACL;
import com.gianghp.hrm_payroll_service.acl.AttendanceSettingACL;
import com.gianghp.hrm_payroll_service.acl.EmployeeACL;
import com.gianghp.hrm_payroll_service.acl.OvertimeACL;
import com.gianghp.hrm_payroll_service.services.DailySalaryService;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaPayrollConsumer {

    private final EmployeeACL employeeACL;
    private final OvertimeACL overtimeACL;
    private final AttendanceACL attendanceACL;
    private final AttendanceSettingACL attendanceSettingACL;
    private final DailySalaryService dailySalaryService;

    @KafkaListener(topics = TopicConstants.EMPLOYEE_CREATED)
    public void consumeEmployeeCreated(EmployeeBasicDto message) {
        log.info("Received from {}: {}", TopicConstants.EMPLOYEE_CREATED, message);
        employeeACL.handleEmployeeCreatedEvent(message);
    }

    @KafkaListener(topics = TopicConstants.EMPLOYEE_CREATE_FAILED)
    public void consumeEmployeeCreateFailed(EmployeeCreateFailedDto message) {
        log.info("Received from {}: {}", TopicConstants.EMPLOYEE_CREATE_FAILED, message);
        employeeACL.handleEmployeeCreateFailedEvent(message);
    }

//    @KafkaListener(topics = TopicConstants.OVERTIME_APPROVED)
//    public void consumeOvertimeApproved(ApprovedOvertimeBasicDto message) {
//        log.info("Received from {}: {}", TopicConstants.OVERTIME_APPROVED, message);
//        overtimeACL.handleOvertimeApprovedEvent(message);
//    }

    @KafkaListener(topics = TopicConstants.FINAL_DAILY_ATTENDANCE)
    public void consumeAttendanceRecorded(AttendanceBasicWrapperDto message) {
        log.info("Received from {}: {}", TopicConstants.ATTENDANCE_RECORDED, message);
        attendanceACL.handleFinalDailyAttendanceEvent(message);
        dailySalaryService.caculateDailySalaryForAllEmployees(LocalDate.now());
    }

    @KafkaListener(topics = TopicConstants.ATTENDANCE_SETTING_CREATED)
    public void consumeAttendanceSettingCreated(AttendanceSettingBasicDto message) {
        log.info("Received from {}: {}", TopicConstants.ATTENDANCE_SETTING_CREATED, message);
        attendanceSettingACL.handleAttendanceSettingCreatedEvent(message);
    }


}
