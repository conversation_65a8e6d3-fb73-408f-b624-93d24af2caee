package com.gianghp.hrm_payroll_service.services;

import com.gianghp.hrm_payroll_service.dtos.TaxDto;
import com.gianghp.hrm_payroll_service.entities.Tax;
import com.gianghp.hrm_payroll_service.mappers.TaxMapper;
import com.gianghp.hrm_payroll_service.repositories.TaxRepository;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TaxService {
  public final TaxRepository taxRepository;
  public final TaxMapper taxMapper;

  public List<TaxDto> getCurrentTaxes() {
    return taxRepository.findByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate.now()).stream()
        .map(taxMapper::toDto)
        .toList();
  }

}
