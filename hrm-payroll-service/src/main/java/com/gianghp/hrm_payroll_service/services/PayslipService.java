package com.gianghp.hrm_payroll_service.services;

import com.gianghp.hrm.enums.PayslipStatus;
import com.gianghp.hrm_payroll_service.dtos.PayslipDto;
import com.gianghp.hrm_payroll_service.entities.DailySalary;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import com.gianghp.hrm_payroll_service.entities.PayrollSetting;
import com.gianghp.hrm_payroll_service.entities.Payslip;
import com.gianghp.hrm_payroll_service.mappers.PayslipMapper;
import com.gianghp.hrm_payroll_service.repositories.DailySalaryRepository;
import com.gianghp.hrm_payroll_service.repositories.EmployeeCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.PayrollSettingRepository;
import com.gianghp.hrm_payroll_service.repositories.PayslipRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PayslipService {

  private final PayslipRepository payslipRepository;
  private final DailySalaryRepository dailySalaryRepository;
  private final PayrollSettingRepository payrollSettingRepository;
  private final EmployeeCacheRepository employeeCacheRepository;
  private final PayslipMapper payslipMapper;


  public Page<PayslipDto> findAllByEmployeeId(UUID employeeId, Pageable pageable) {
    return payslipRepository.findAllByEmployee_Id(employeeId, pageable).map(payslipMapper::toDto);
  }

  public PayslipDto getCurrentPayslipForEmployee(UUID employeeId) {
    return payslipMapper.toDto(
        payslipRepository.findTopByEmployee_IdOrderByIssuedDateDesc(employeeId));
  }

  public PayslipDto getPayslipByIssuedDate(UUID employeeId, LocalDate issuedDate) {
    return payslipMapper.toDto(
        payslipRepository.findByEmployee_IdAndIssuedDate(employeeId, issuedDate));
  }


  public PayslipDto generatePayslipForEmployee(UUID employeeId, YearMonth yearMonth) {

    if (payslipRepository.existsByEmployee_IdAndMonth(employeeId, yearMonth)) {
      throw new RuntimeException(
          "Payslip already exists for employee " + employeeId + " in month " + yearMonth);
    }

    EmployeeCache employeeCache = employeeCacheRepository.findById(employeeId)
        .orElseThrow(() -> new RuntimeException("Employee not found"));

    PayrollSetting payrollSetting = payrollSettingRepository.findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
        yearMonth.atDay(1));
    if (payrollSetting == null) {
      log.warn("Payroll setting not found for employee {} on {}", employeeId, yearMonth);
      return null;
    }

    LocalDate startDate = yearMonth.minusMonths(1).atDay(payrollSetting.getCutOffDay());
    LocalDate endDate = yearMonth.atDay(payrollSetting.getCutOffDay());

    List<DailySalary> dailySalaries = dailySalaryRepository.findByAttendance_EmployeeCache_IdAndAttendance_DateBetween(
        employeeId, startDate, endDate);

    if (dailySalaries.isEmpty()) {
      log.warn("Daily salary not found for employee {} from {} to {}", employeeId, startDate,
          endDate);
      return null;
    }

    BigDecimal grossSalary = dailySalaries.stream().map(DailySalary::getGrossAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal totalDeduction = dailySalaries.stream().map(DailySalary::getTotalDeduction)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal totalTax = dailySalaries.stream().map(DailySalary::getTotalTax)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal netSalary = dailySalaries.stream().map(DailySalary::getNetAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    Payslip payslip = Payslip.builder()
        .employee(employeeCache)
        .grossSalary(grossSalary)
        .totalDeduction(totalDeduction)
        .totalTax(totalTax)
        .netSalary(netSalary)
        .issuedDate(LocalDate.now())
        .status(PayslipStatus.GENERATED)
        .note("Generated automatically, month: " + yearMonth)
        .build();

    payslipRepository.save(payslip);

    return payslipMapper.toDto(payslip);
  }


}
