package com.gianghp.hrm_payroll_service.services;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.utils.DateTimeUtils;
import com.gianghp.hrm_payroll_service.dtos.DailySalaryDto;
import com.gianghp.hrm_payroll_service.dtos.WeeklySalaryDto;
import com.gianghp.hrm_payroll_service.entities.AttendanceForPayrollCache;
import com.gianghp.hrm_payroll_service.entities.AttendanceSettingCache;
import com.gianghp.hrm_payroll_service.entities.BaseSalary;
import com.gianghp.hrm_payroll_service.entities.DailySalary;
import com.gianghp.hrm_payroll_service.entities.DailySalaryFailedLog;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import com.gianghp.hrm_payroll_service.entities.Tax;
import com.gianghp.hrm_payroll_service.mappers.DailySalaryMapper;
import com.gianghp.hrm_payroll_service.repositories.AttendanceForPayrollCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.AttendanceSettingCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.BaseSalaryRepository;
import com.gianghp.hrm_payroll_service.repositories.DailySalaryFailedLogRepository;
import com.gianghp.hrm_payroll_service.repositories.DailySalaryRepository;
import com.gianghp.hrm_payroll_service.repositories.EmployeeCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.TaxRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DailySalaryService {

  private final BaseSalaryRepository baseSalaryRepository;
  private final TaxRepository taxRepository;
  private final DailySalaryRepository dailySalaryRepository;
  private final EmployeeCacheRepository employeeCacheRepository;
  private final AttendanceForPayrollCacheRepository attendanceForPayrollCacheRepository;
  private final AttendanceSettingCacheRepository attendanceSettingCacheRepository;
  private final DailySalaryFailedLogRepository failedLogRepository;
  private final DailySalaryMapper dailySalaryMapper;

  public Page<DailySalaryDto> findAll(Pageable pageable) {
    return dailySalaryRepository.findAll(pageable).map(dailySalaryMapper::toDto);
  }

  public Page<DailySalaryDto> findByEmployeeIdAndMonth(UUID employeeId, YearMonth yearMonth,
      Pageable pageable) {
    LocalDate startDate = yearMonth.atDay(1);
    LocalDate endDate = yearMonth.atEndOfMonth();
    return dailySalaryRepository.findByAttendance_EmployeeCache_IdAndAttendance_DateBetween(
        employeeId, startDate, endDate, pageable).map(dailySalaryMapper::toDto);
  }

  public void caculateDailySalaryForAllEmployees(LocalDate localDate) {

    DayOfWeek dayOfWeek = localDate.getDayOfWeek();

    if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
      System.out.println("Skipping salary calculation for weekend: " + localDate);
      return;
    }

    List<DailySalary> dailySalaries = new ArrayList<>();
    AttendanceSettingCache attendanceSettingCache = attendanceSettingCacheRepository.findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
        localDate);

    YearMonth yearMonth = YearMonth.from(localDate);
    int daysInMonth = DateTimeUtils.calculateWorkingDaysInMonth(yearMonth);

    List<EmployeeCache> allEmployees = employeeCacheRepository.findAll();
    List<Tax> currentTaxes = taxRepository.findByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
        localDate);

    for (EmployeeCache employee : allEmployees) {

      AttendanceForPayrollCache attendance = attendanceForPayrollCacheRepository.findByEmployeeCache_IdAndDate(
          employee.getId(), localDate);

      if (attendance == null) {
        log.warn("Attendance not found for employee {} on {}", employee.getId(), localDate);
        continue;
      }

      try {
        BaseSalary baseSalary = baseSalaryRepository.findTopByEmployee_IdAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
            employee.getId(), localDate
        );

        if (baseSalary == null) {
          log.warn("Base salary not found for employee {} on {}", employee.getId(), localDate);
          throw new RuntimeException(
              "Base salary not found for employee " + employee.getEmployeeCode());
        }

        DailySalary dailySalary = new DailySalary();
        dailySalary.setAttendance(attendance);

        BigDecimal dailyBaseSalary = baseSalary.getAmount()
            .divide(BigDecimal.valueOf(daysInMonth), 2, RoundingMode.HALF_UP);

        if (attendance.getCheckinStatus() == CheckinStatus.HOLIDAY) {
          dailySalary.setGrossAmount(dailyBaseSalary);
          dailySalary.setNetAmount(dailyBaseSalary);
          dailySalary.setTotalDeduction(BigDecimal.ZERO);
          dailySalary.setTotalTax(dailyBaseSalary.multiply(
              BigDecimal.valueOf(currentTaxes.stream().mapToDouble(Tax::getRate).sum())));
          dailySalary.setNote("Holiday");
          dailySalaries.add(dailySalary);
          continue;
        }

        String note = "checkin status: " + attendance.getCheckinStatus() + ", checkout status: "
            + ", standard working hours: "
            + attendance.getStandardWorkingHours()
            + attendance.getCheckoutStatus() + ", late deduction rate: "
            + attendance.getTotalLateDeductionRate() + ", early out deduction rate: "
            + attendance.getTotalEarlyOutDeductionRate() + ", overtime hours: "
            + attendance.getOvertimeHours() + ", overtime rate: " + attendance.getOvertimeRate();

        if (attendance.getStandardWorkingHours()
            .compareTo(BigDecimal.valueOf(attendanceSettingCache.getMinWorkingMinutes() / 60))
            < 0) {
          dailySalary.setNetAmount(BigDecimal.ZERO);
          dailySalary.setGrossAmount(BigDecimal.ZERO);
          dailySalary.setTotalDeduction(BigDecimal.ZERO);
          dailySalary.setTotalTax(BigDecimal.ZERO);
          dailySalary.setNote(note);
          dailySalaries.add(dailySalary);
          continue;
        }

        BigDecimal hourlySalary = dailyBaseSalary.divide(
            attendanceSettingCache.getStandardWorkingHours(), 2, RoundingMode.HALF_UP);

        BigDecimal totalDeduction = dailyBaseSalary.multiply(
            attendance.getTotalLateDeductionRate().add(attendance.getTotalEarlyOutDeductionRate()));

        BigDecimal totalAmountWithoutTaxAndDeduction = dailyBaseSalary.add(
            attendance.getOvertimeHours().multiply(hourlySalary)
                .multiply(attendance.getOvertimeRate()));

        BigDecimal totalAmountWithoutTax = totalAmountWithoutTaxAndDeduction.subtract(
            totalDeduction);

        BigDecimal totalTax = totalAmountWithoutTax.multiply(
            BigDecimal.valueOf(currentTaxes.stream().mapToDouble(Tax::getRate).sum()));

        BigDecimal finalAmount = totalAmountWithoutTax.subtract(
            totalTax
        );

        dailySalary.setGrossAmount(totalAmountWithoutTaxAndDeduction);
        dailySalary.setTotalDeduction(totalDeduction);
        dailySalary.setTotalTax(totalTax);
        dailySalary.setNetAmount(finalAmount);
        dailySalary.setNote(note);

        dailySalaries.add(dailySalary);

      } catch (Exception ex) {
        log.error("Failed to calculate daily salary for employee {} on {}: {}",
            employee.getId(), localDate, ex.getMessage(), ex);

        failedLogRepository.save(
            DailySalaryFailedLog.builder()
                .attendance(attendance)
                .attendanceDate(attendance.getDate())
                .reason(ex.getMessage())
                .resolved(false)
                .build()
        );
      }
    }

    dailySalaryRepository.saveAll(dailySalaries);
  }

  public List<DailySalaryDto> getByEmployeeIdAndWeeklySalary(UUID employeeId, LocalDate startDate) {
    LocalDate endDate = startDate.with(DayOfWeek.FRIDAY);
    return dailySalaryRepository.findByAttendance_EmployeeCache_IdAndAttendance_DateBetweenOrderByAttendance_DateAsc(
        employeeId, startDate, endDate).stream().map(dailySalaryMapper::toDto).toList();
  }


  public Page<WeeklySalaryDto> getWeekylySalary(LocalDate localDate, Pageable pageable) {
    List<WeeklySalaryDto> weeklySalaryDtos = new ArrayList<>();

    LocalDate startDate = localDate.with(DayOfWeek.MONDAY);
    LocalDate endDate = localDate.with(DayOfWeek.FRIDAY);

    Page<EmployeeCache> employees = employeeCacheRepository.findAll(pageable);

    for (EmployeeCache employee : employees.getContent()) {
      List<DailySalary> dailySalaries = dailySalaryRepository
          .findByAttendance_EmployeeCache_IdAndAttendance_DateBetweenOrderByAttendance_DateAsc(
              employee.getId(), startDate, endDate);

      // Map mặc định cho đủ 7 ngày
      Map<DayOfWeek, BigDecimal> salaryMap = new EnumMap<>(DayOfWeek.class);
      for (DayOfWeek dow : DayOfWeek.values()) {
        salaryMap.put(dow, BigDecimal.ZERO); // hoặc "—" nếu muốn show rỗng
      }

      // Ghi đè các ngày có dữ liệu thực tế
      for (DailySalary ds : dailySalaries) {
        DayOfWeek dow = ds.getAttendance().getDate().getDayOfWeek();
        salaryMap.put(dow, ds.getNetAmount());
      }

      // Tìm các ngày bị thiếu (vẫn = "0")
      List<String> missingDays = salaryMap.entrySet().stream()
          .filter(e -> e.getValue().compareTo(BigDecimal.ZERO) == 0)
          .map(e -> e.getKey().name()) // MONDAY, TUESDAY...
          .toList();

      // Build DTO
      WeeklySalaryDto dto = new WeeklySalaryDto();
      dto.setEmployeeId(employee.getId());
      dto.setEmployeeCode(employee.getEmployeeCode());
      dto.setEmployeeName(employee.getFullName());

      dto.setMon(salaryMap.get(DayOfWeek.MONDAY));
      dto.setTue(salaryMap.get(DayOfWeek.TUESDAY));
      dto.setWed(salaryMap.get(DayOfWeek.WEDNESDAY));
      dto.setThu(salaryMap.get(DayOfWeek.THURSDAY));
      dto.setFri(salaryMap.get(DayOfWeek.FRIDAY));

      dto.setMissingData(!missingDays.isEmpty());
      dto.setMissingDays(missingDays);

      dto.setStartDate(startDate);

      weeklySalaryDtos.add(dto);
    }


    return new PageImpl<>(weeklySalaryDtos, pageable, employees.getTotalElements());
  }


}



