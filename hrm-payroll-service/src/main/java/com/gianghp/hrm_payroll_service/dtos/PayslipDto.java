package com.gianghp.hrm_payroll_service.dtos;

import com.gianghp.hrm.enums.PayslipStatus;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayslipDto {
  private String employeeCode;
  private String employeeName;
  private Double grossSalary;
  private Double totalDeduction;
  private Double totalTax;
  private Double netSalary;
  private LocalDate issuedDate;
  private PayslipStatus status;
  private String note;
}
