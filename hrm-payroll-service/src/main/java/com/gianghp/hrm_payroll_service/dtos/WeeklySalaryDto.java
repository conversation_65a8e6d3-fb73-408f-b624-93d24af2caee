package com.gianghp.hrm_payroll_service.dtos;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeeklySalaryDto {
  private UUID employeeId;
  private String employeeCode;
  private String employeeName;

  private BigDecimal mon;
  private BigDecimal tue;
  private BigDecimal wed;
  private BigDecimal thu;
  private BigDecimal fri;

  private LocalDate startDate;

  private boolean missingData;       // flag nếu thiếu ngày

  private List<String> missingDays;  // danh sách ngày thiếu (MON, TUE...)
}

