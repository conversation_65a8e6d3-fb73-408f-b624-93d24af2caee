package com.gianghp.hrm_payroll_service.dtos;

import com.gianghp.hrm.enums.CurrencyCode;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayrollSettingDto {
  private Integer cutOffDay;
  private Integer payDay;
  private LocalDate effectiveDate;
  private CurrencyCode currency = CurrencyCode.VND; // default
}
