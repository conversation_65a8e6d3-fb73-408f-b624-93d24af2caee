package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm_payroll_service.dtos.PayslipDto;
import com.gianghp.hrm_payroll_service.entities.Payslip;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface PayslipMapper {
  PayslipMapper INSTANCE = Mappers.getMapper(PayslipMapper.class);

  @Mapping(target = "employeeCode", expression = "java(payslip.getEmployee().getEmployeeCode())")
  @Mapping(target = "employeeName", expression = "java(payslip.getEmployee().getFullName())")
  PayslipDto toDto(Payslip payslip);

}
