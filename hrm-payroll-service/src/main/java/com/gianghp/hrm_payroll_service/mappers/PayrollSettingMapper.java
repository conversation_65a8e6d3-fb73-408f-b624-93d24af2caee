package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm_payroll_service.dtos.PayrollSettingDto;
import com.gianghp.hrm_payroll_service.entities.PayrollSetting;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface PayrollSettingMapper {

    PayrollSettingMapper INSTANCE = Mappers.getMapper(PayrollSettingMapper.class);

    PayrollSettingDto toDto(PayrollSetting payrollSetting);
}
