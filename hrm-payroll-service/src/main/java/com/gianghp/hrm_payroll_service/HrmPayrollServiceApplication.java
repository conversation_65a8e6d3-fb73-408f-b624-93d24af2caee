package com.gianghp.hrm_payroll_service;

import com.gianghp.hrm.configs.SimpleSwaggerConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * Employee Service Application
 * Sử dụng hrm-common cho JWT validation
 */
@Import(SimpleSwaggerConfig.class)
@SpringBootApplication
@EnableJpaRepositories(basePackages = "com.gianghp.hrm_payroll_service.repositories")
@EntityScan("com.gianghp.hrm_payroll_service.entities")
@ComponentScan(basePackages = {
        "com.gianghp.hrm_payroll_service",
    "com.gianghp.hrm"
})
public class HrmPayrollServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(HrmPayrollServiceApplication.class, args);
    }
}
