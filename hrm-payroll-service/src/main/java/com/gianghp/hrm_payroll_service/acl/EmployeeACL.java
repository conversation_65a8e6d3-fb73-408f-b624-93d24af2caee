package com.gianghp.hrm_payroll_service.acl;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import com.gianghp.hrm_payroll_service.entities.BaseSalary;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import com.gianghp.hrm_payroll_service.mappers.BaseSalaryMapper;
import com.gianghp.hrm_payroll_service.mappers.EmployeeCacheMapper;
import com.gianghp.hrm_payroll_service.producers.KafkaPayrollProducer;
import com.gianghp.hrm_payroll_service.repositories.BaseSalaryRepository;
import com.gianghp.hrm_payroll_service.repositories.EmployeeCacheRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmployeeACL {
    private final EmployeeCacheRepository employeeCacheRepository;
    private final BaseSalaryRepository baseSalaryRepository;
    private final BaseSalaryMapper baseSalaryMapper;
    private final EmployeeCacheMapper employeeCacheMapper;
    private final KafkaPayrollProducer kafkaPayrollProducer;

    public void handleEmployeeCreatedEvent(EmployeeBasicDto employeeBasicDto) {
        if (employeeCacheRepository.existsByEmployeeCode(employeeBasicDto.getEmployeeCode())) {
            log.info("Employee cache already exists, skipping");
            return;
        }
        try {
            EmployeeCache employeeCache = employeeCacheMapper.toEntity(employeeBasicDto);
            BaseSalary baseSalary = baseSalaryMapper.toEntity(employeeBasicDto.getBaseSalary());
            baseSalary.setEmployee(employeeCache);
            employeeCacheRepository.save(employeeCache);
            baseSalaryRepository.save(baseSalary);
            log.info("Employee cache {} and base salary {} created successfully", employeeCache, baseSalary);
        }
        catch (Exception e) {
            log.error("Failed to create employee cache: {}", e.getMessage());
            kafkaPayrollProducer.sendEmployeeWhenCreatedFailed(employeeBasicDto);
        }
    }

    public void handleEmployeeCreateFailedEvent(EmployeeCreateFailedDto message) {
        try {
            UUID employeeId = message.getEmployeeId();
            if (employeeId == null) {
                log.error("Employee id is null, cannot delete user");
                return;
            }
            log.info("Employee create failed, deleting user with id: {}", employeeId);
            employeeCacheRepository.deleteById(employeeId);
        } catch (Exception e) {
            log.error("Failed to delete user: {}", e.getMessage());
        }
    }
}
