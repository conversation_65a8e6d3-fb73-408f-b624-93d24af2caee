# syntax=docker/dockerfile:1

FROM eclipse-temurin:21-jdk-jammy AS deps
WORKDIR /build
COPY --chmod=0755 gradlew gradlew
COPY gradle/ gradle/
COPY build.gradle .
COPY hrm-common/ hrm-common/
COPY settings.gradle .

RUN --mount=type=cache,target=/root/.gradle \
    ./gradlew dependencies --no-daemon

FROM deps AS package
WORKDIR /build
COPY . .
RUN --mount=type=cache,target=/root/.gradle \
    ./gradlew bootJar -x test --no-daemon && \
    cp build/libs/*.jar app.jar


FROM package AS extract
WORKDIR /build
RUN java -Djarmode=layertools -jar app.jar extract --destination build/extracted

FROM extract AS development
WORKDIR /build
RUN cp -r build/extracted/dependencies/. ./
RUN cp -r build/extracted/spring-boot-loader/. ./
RUN cp -r build/extracted/snapshot-dependencies/. ./
RUN cp -r build/extracted/application/. ./
ENV JAVA_TOOL_OPTIONS -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000
EXPOSE 6040
CMD ["java", "-Dspring.profiles.active=postgres", "org.springframework.boot.loader.launch.JarLauncher"]

FROM eclipse-temurin:21-jre-jammy AS final
ARG UID=10001
RUN adduser \
    --disabled-password \
    --gecos "" \
    --home "/nonexistent" \
    --shell "/sbin/nologin" \
    --no-create-home \
    --uid "${UID}" \
    appuser
USER appuser
COPY --from=extract /build/build/extracted/dependencies/ ./
COPY --from=extract /build/build/extracted/spring-boot-loader/ ./
COPY --from=extract /build/build/extracted/snapshot-dependencies/ ./
COPY --from=extract /build/build/extracted/application/ ./
EXPOSE 8080
ENTRYPOINT ["java", "-Dspring.profiles.active=postgres", "org.springframework.boot.loader.launch.JarLauncher"]
