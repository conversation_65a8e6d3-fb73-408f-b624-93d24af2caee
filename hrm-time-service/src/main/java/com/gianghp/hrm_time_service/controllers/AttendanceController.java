package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_time_service.dtos.get.AttendanceDto;
import com.gianghp.hrm_time_service.dtos.params.AdminAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyAttendanceRequestParams;
import com.gianghp.hrm_time_service.services.AttendanceService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/attendances")
@RequiredArgsConstructor
public class AttendanceController {

    private final AttendanceService attendanceService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<AttendanceDto>>> getAll(@ParameterObject AdminAttendanceRequestParams params) {
        try {
            Page<AttendanceDto> attendanceDtos = attendanceService.findByParams(params);
            ApiResponse<List<AttendanceDto>> response = ApiResponse.success(
                    "Get all attendances successfully",
                    attendanceDtos.getContent(),
                    attendanceDtos.getTotalElements(),
                    attendanceDtos.getTotalPages(),
                    attendanceDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve attendances: " + e.getMessage()));
        }
    }


    @GetMapping("/employee/{employeeId}")
    public ResponseEntity<ApiResponse<List<AttendanceDto>>> getAttendancesByEmployee(
            @PathVariable UUID employeeId,
            @ModelAttribute AdminAttendanceRequestParams params
    ) {
        try {
            // Set the employeeId from path variable
            params.setEmployeeId(employeeId);

            Page<AttendanceDto> attendanceDtos = attendanceService.findByParams(params);

            ApiResponse<List<AttendanceDto>> response = ApiResponse.success(
                    "Get attendances by employee successfully",
                    attendanceDtos.getContent(),
                    attendanceDtos.getTotalElements(),
                    attendanceDtos.getTotalPages(),
                    attendanceDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve attendances: " + e.getMessage()));
        }
    }

    @GetMapping("/me")
    public ResponseEntity<ApiResponse<List<AttendanceDto>>> getMyAttendances(
            @ParameterObject MyAttendanceRequestParams params,
            @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);

            Page<AttendanceDto> attendanceDtos = attendanceService.findMyByParams(userId, params);

            ApiResponse<List<AttendanceDto>> response = ApiResponse.success(
                    "Get attendances by employee successfully",
                    attendanceDtos.getContent(),
                    attendanceDtos.getTotalElements(),
                    attendanceDtos.getTotalPages(),
                    attendanceDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve attendances: " + e.getMessage()));
        }
    }


    @PostMapping("/me/create-and-check-in")
    public ResponseEntity<ApiResponse<AttendanceDto>> createAttendance(
            @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            attendanceService.myCreateAndCheckIn(userId);
            return ResponseEntity.ok(ApiResponse.success("Attendance created successfully", null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to create attendance: " + e.getMessage()));
        }
    }

    @PutMapping("/me/check-out")
    public ResponseEntity<ApiResponse<AttendanceDto>> checkOut(
            @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            attendanceService.myCheckOut(userId);
            return ResponseEntity.ok(ApiResponse.success("Checked out successfully", null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to check out: " + e.getMessage()));
        }
    }

    @GetMapping("/manager/employees")
    public ResponseEntity<ApiResponse<List<AttendanceDto>>> getManagerEmployeeAttendances(
            @ParameterObject ManagerAttendanceRequestParams params,
            @Parameter(hidden = true)  @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            Page<AttendanceDto> attendanceDtos = attendanceService.findByManagerParams(userId, params);

            ApiResponse<List<AttendanceDto>> response = ApiResponse.success(
                    "Get attendances by employee successfully",
                    attendanceDtos.getContent(),
                    attendanceDtos.getTotalElements(),
                    attendanceDtos.getTotalPages(),
                    attendanceDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve attendances: " + e.getMessage()));
        }
    }

    @PutMapping("/test/auto-check-out-all")
    public ResponseEntity<ApiResponse<String>> autoCheckOutAll() {
        try {
            attendanceService.autoCheckOutAllAndCalculateWorkingHours();
            return ResponseEntity.ok(ApiResponse.success("Auto check out all successfully", null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to auto check out all: " + e.getMessage()));
        }
    }

    @PutMapping("/test/auto-mark-absent-all")
    public ResponseEntity<ApiResponse<String>> autoMarkAbsentAll() {
        try {
            attendanceService.autoMarkAbsentAll();
            return ResponseEntity.ok(ApiResponse.success("Auto mark absent all successfully", null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to auto mark absent all: " + e.getMessage()));
        }
    }

}
