package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_time_service.dtos.get.EmployeeDashboardTimeDto;
import com.gianghp.hrm_time_service.dtos.get.WorkingSummaryThisMonthDto;
import com.gianghp.hrm_time_service.services.DashBoardService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class DashBoardController {
    private final DashBoardService dashBoardService;

    @GetMapping("/employee/{employeeId}")
    public ResponseEntity<ApiResponse<EmployeeDashboardTimeDto>> getEmployeeDashBoardInfo(@PathVariable UUID employeeId) {
        try {
            EmployeeDashboardTimeDto employeeDashboardTimeDto = dashBoardService.getEmployeeDashBoardInfo(employeeId);
            return ResponseEntity.ok(ApiResponse.success("Employee dashboard retrieved successfully", employeeDashboardTimeDto));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve employee dashboard: " + e.getMessage()));
        }
    }

    @GetMapping("/employee/{employeeId}/working-summary")
    public ResponseEntity<ApiResponse<WorkingSummaryThisMonthDto>> getWorkingSummaryThisMonth(@PathVariable UUID employeeId) {
        try {
            WorkingSummaryThisMonthDto workingSummaryThisMonthDto = dashBoardService.getWorkingSummaryThisMonth(employeeId);
            return ResponseEntity.ok(ApiResponse.success("Working summary retrieved successfully", workingSummaryThisMonthDto));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve working summary: " + e.getMessage()));
        }
    }

    @GetMapping("/me")
    public ResponseEntity<ApiResponse<EmployeeDashboardTimeDto>> getMyDashBoardInfo(
            @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            EmployeeDashboardTimeDto employeeDashboardTimeDto = dashBoardService.getMyDashBoardInfo(userId);
            return ResponseEntity.ok(ApiResponse.success("Employee dashboard retrieved successfully", employeeDashboardTimeDto));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve employee dashboard: " + e.getMessage()));
        }
    }

    @GetMapping("/me/working-summary")
    public ResponseEntity<ApiResponse<WorkingSummaryThisMonthDto>> getMyWorkingSummaryThisMonth(
            @Parameter(hidden = true) @RequestHeader("X-User-Sub") String userIdHeader
    ) {
        try {
            UUID userId = UUID.fromString(userIdHeader);
            WorkingSummaryThisMonthDto workingSummaryThisMonthDto = dashBoardService.getMyWorkingSummaryThisMonth(userId);
            return ResponseEntity.ok(ApiResponse.success("Working summary retrieved successfully", workingSummaryThisMonthDto));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve working summary: " + e.getMessage()));
        }
    }
}
