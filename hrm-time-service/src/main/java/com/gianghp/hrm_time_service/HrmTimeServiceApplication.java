package com.gianghp.hrm_time_service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gianghp.hrm.configs.SimpleSwaggerConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Employee Service Application
 * Sử dụng hrm-common cho JWT validation
 */

@Import(SimpleSwaggerConfig.class)
@EnableScheduling
@SpringBootApplication
@EnableJpaRepositories(basePackages = "com.gianghp.hrm_time_service.repositories")
@EntityScan("com.gianghp.hrm_time_service.entities")
@ComponentScan(basePackages = {
        "com.gianghp.hrm_time_service",
    "com.gianghp.hrm"
})
public class HrmTimeServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(HrmTimeServiceApplication.class, args);
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return mapper;
    }

}
