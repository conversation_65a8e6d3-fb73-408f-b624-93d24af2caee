package com.gianghp.hrm_time_service.acl;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import com.gianghp.hrm_time_service.consumers.KafkaTimeConsumer;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import com.gianghp.hrm_time_service.mappers.EmployeeCacheMapper;
import com.gianghp.hrm_time_service.producers.KafkaTimeProducer;
import com.gianghp.hrm_time_service.repositories.EmployeeCacheRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmployeeACL {
    private final KafkaTimeProducer kafkaTimeProducer;
    private final EmployeeCacheRepository employeeCacheRepository;
    private final EmployeeCacheMapper employeeCacheMapper;

    public void handleEmployeeCreatedEvent(EmployeeBasicDto employeeBasicDto) {
        if (employeeCacheRepository.existsByEmployeeCode(employeeBasicDto.getEmployeeCode())) {
            log.info("Employee cache already exists, skipping");
            return;
        }
        try {
            EmployeeCache employeeCache = employeeCacheMapper.toEntity(employeeBasicDto);
            employeeCacheRepository.save(employeeCache);
            log.info("Employee cache created {} successfully", employeeCache);
        }
        catch (Exception e) {
            log.error("Failed to create employee cache: {}", e.getMessage());
            kafkaTimeProducer.sendEmployeeWhenCreatedFailed(employeeBasicDto);
        }
    }

    public void handleEmployeeCreateFailedEvent(EmployeeCreateFailedDto message) {
        try {
            UUID employeeId = message.getEmployeeId();
            if (employeeId == null) {
                log.error("Employee id is null, cannot delete user");
                return;
            }
            log.info("Employee create failed, deleting user with id: {}", employeeId);
            employeeCacheRepository.deleteById(employeeId);
        } catch (Exception e) {
            log.error("Failed to delete user: {}", e.getMessage());
        }
    }
}
