package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm.enums.CommonLeaveType;
import com.gianghp.hrm.enums.LeaveRequestStatus;
import com.gianghp.hrm.exceptions.BadRequestException;
import com.gianghp.hrm_time_service.dtos.get.TotalLeaveToday;
import com.gianghp.hrm_time_service.dtos.post.EmployeeLeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.LeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.get.LeaveRequestDto;
import com.gianghp.hrm_time_service.dtos.params.ManagerLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.AdminLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.get.LeaveThisMonthDto;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import com.gianghp.hrm_time_service.entities.LeaveRequest;
import com.gianghp.hrm_time_service.entities.LeaveType;
import com.gianghp.hrm_time_service.mappers.AdminLeaveRequestParamsMapper;
import com.gianghp.hrm_time_service.mappers.LeaveRequestMapper;
import com.gianghp.hrm_time_service.repositories.EmployeeCacheRepository;
import com.gianghp.hrm_time_service.repositories.LeaveRequestRepository;
import com.gianghp.hrm_time_service.repositories.LeaveTypeRepository;
import com.gianghp.hrm_time_service.specifications.LeaveRequestSpecification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.gianghp.hrm.utils.StringUtils.normalizeToEnumName;

@Service
@RequiredArgsConstructor
@Slf4j
public class LeaveRequestService {

    private final LeaveRequestRepository leaveRequestRepository;
    private final LeaveRequestMapper leaveRequestMapper;
    private final LeaveTypeRepository leaveTypeRepository;
    private final EmployeeCacheRepository employeeCacheRepository;
    private final AdminLeaveRequestParamsMapper adminLeaveRequestParamsMapper;

    public Page<LeaveRequestDto> findAll(Pageable pageable) {
        return leaveRequestRepository.findAll(pageable).map(leaveRequestMapper::toDto);
    }

    public LeaveRequestDto findById(UUID id) {
        return leaveRequestRepository.findById(id).map(leaveRequestMapper::toDto).orElseThrow(() -> new RuntimeException("Leave request not found"));
    }

    public Page<LeaveRequestDto> findByDepartment(UUID departmentId, Pageable pageable) {
        return leaveRequestRepository.findByEmployeeCache_DepartmentId(departmentId, pageable).map(leaveRequestMapper::toDto);
    }

    public LeaveThisMonthDto getLeaveThisMonth(UUID employeeId) {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        List<LeaveRequest> leaveRequests = leaveRequestRepository.findByEmployeeCache_IdAndStatusAndStartDateBetween(employeeId, LeaveRequestStatus.APPROVED, startOfMonth, endOfMonth);
        int sickLeave = 0;
        int casualLeave = 0;
        int otherLeave = 0;
        int annualLeave = 0;
        for (LeaveRequest leaveRequest : leaveRequests) {
            if (normalizeToEnumName(leaveRequest.getLeaveType().getName())
                    .equals(CommonLeaveType.SICK_LEAVE.name())) {
                sickLeave += leaveRequest.getNumberOfDays();
            } else if (normalizeToEnumName(leaveRequest.getLeaveType().getName())
                    .equals(CommonLeaveType.CASUAL_LEAVE.name())) {
                casualLeave += leaveRequest.getNumberOfDays();
            }
            else if (normalizeToEnumName(leaveRequest.getLeaveType().getName())
                    .equals(CommonLeaveType.ANNUAL_LEAVE.name())) {
                annualLeave += leaveRequest.getNumberOfDays();
            }
            else {
                otherLeave += leaveRequest.getNumberOfDays();
            }
        }
        return new LeaveThisMonthDto(annualLeave, sickLeave, casualLeave, otherLeave);
    }

    public Page<LeaveRequestDto> findByEmployeeIdByFilter(
            UUID employeeId,
            Optional<LocalDate> start,
            Optional<LocalDate> end,
            Optional<YearMonth> month,
            Pageable pageable
    ) {
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        if (start.isPresent()) {
            startDateTime = start.get().atStartOfDay();
            endDateTime = end
                    .map(e -> e.atTime(LocalTime.MAX))
                    .orElse(start.get().withDayOfMonth(start.get().lengthOfMonth()).atTime(LocalTime.MAX));
        } else if (month.isPresent()) {
            YearMonth ym = month.get();
            startDateTime = ym.atDay(1).atStartOfDay();
            endDateTime = ym.atEndOfMonth().atTime(LocalTime.MAX);
        }

        if (startDateTime != null) {
            return leaveRequestRepository
                    .findByEmployeeCache_IdAndStartDateBetween(employeeId, startDateTime, endDateTime, pageable)
                    .map(leaveRequestMapper::toDto);
        }

        return leaveRequestRepository
                .findByEmployeeCache_Id(employeeId, pageable)
                .map(leaveRequestMapper::toDto);
    }



    public Page<LeaveRequestDto> findByStatus(LeaveRequestStatus status, Pageable pageable) {
        return leaveRequestRepository.findByStatus(status, pageable).map(leaveRequestMapper::toDto);
    }

    public LeaveRequestDto create(LeaveRequestCreateDto createDto) {
        LeaveType leaveType = leaveTypeRepository.findById(createDto.getLeaveTypeId()).orElseThrow(() -> new RuntimeException("Leave type not found"));
        EmployeeCache employeeCache = employeeCacheRepository.findById(createDto.getEmployeeId()).orElseThrow(() -> new RuntimeException("Employee not found"));
        EmployeeCache approver = employeeCacheRepository.findById(createDto.getApproverId()).orElse(null);
        int numberOfDays = calculateLeaveDays(createDto.getStartDate(), createDto.getEndDate());
        LeaveRequest leaveRequest = leaveRequestMapper.toEntity(createDto);
        leaveRequest.setLeaveType(leaveType);
        leaveRequest.setEmployeeCache(employeeCache);
        leaveRequest.setApprover(approver);
        leaveRequest.setNumberOfDays(numberOfDays);
        leaveRequestRepository.save(leaveRequest);
        return leaveRequestMapper.toDto(leaveRequest);
    }


    public void approve(UUID id, UUID approverId) {
        LeaveRequest leaveRequest = leaveRequestRepository.findById(id).orElseThrow(() -> new RuntimeException("Leave request not found"));
        if (leaveRequest.getStatus() != LeaveRequestStatus.PENDING) {
            throw new RuntimeException("Leave request cannot be approved");
        }
        if (leaveRequest.getApprover() != null && !leaveRequest.getApprover().getUserId().equals(approverId)) {
            throw new RuntimeException("Leave request cannot be approved by another approver");
        }
        leaveRequest.setStatus(LeaveRequestStatus.APPROVED);
        leaveRequest.setApprover(employeeCacheRepository.findByUserId(approverId).orElseThrow(() -> new RuntimeException("Approver not found")));
        leaveRequest.setApprovedAt(LocalDate.now());
        leaveRequestRepository.save(leaveRequest);
    }

    public void reject(UUID id, UUID approverId) {
        LeaveRequest leaveRequest = leaveRequestRepository.findById(id).orElseThrow(() -> new RuntimeException("Leave request not found"));
        if (leaveRequest.getStatus() != LeaveRequestStatus.PENDING) {
            throw new RuntimeException("Leave request cannot be rejected");
        }
        if (leaveRequest.getApprover() != null && !leaveRequest.getApprover().getUserId().equals(approverId)) {
            throw new RuntimeException("Leave request cannot be rejected by another approver");
        }
        leaveRequest.setStatus(LeaveRequestStatus.REJECTED);
        leaveRequest.setApprover(employeeCacheRepository.findByUserId(approverId).orElseThrow(() -> new RuntimeException("Approver not found")));
        leaveRequest.setApprovedAt(LocalDate.now());
        leaveRequestRepository.save(leaveRequest);
    }


    public Page<LeaveRequestDto> findByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return leaveRequestRepository.findByStartDateBetween(startDate, endDate, pageable).map(leaveRequestMapper::toDto);
    }

    public Page<LeaveRequestDto> findByParams(AdminLeaveRequestParams params) {
        // Map field names for native query
        String sortField = params.getSortBy();

        Pageable pageable = PageRequest.of(
                params.getPage(),
                params.getSize(),
                params.getSortDir().equalsIgnoreCase("desc")
                        ? Sort.by(sortField).descending()
                        : Sort.by(sortField).ascending()
        );

        // Handle month and year parameters
        if (params.getMonth() != null && !params.getMonth().isBlank()) {
            YearMonth ym = YearMonth.parse(params.getMonth());
            params.setStartDate(ym.atDay(1));
            params.setEndDate(ym.atEndOfMonth());
        }


        // Handle special filters
        if (params.getIsCurrentMonth() != null && params.getIsCurrentMonth()) {
            LocalDate now = LocalDate.now();
            params.setStartDate(now.withDayOfMonth(1));
            params.setEndDate(now.withDayOfMonth(now.lengthOfMonth()));
        }

        if (params.getIsCurrentYear() != null && params.getIsCurrentYear()) {
            int currentYear = LocalDate.now().getYear();
            params.setStartDate(LocalDate.of(currentYear, 1, 1));
            params.setEndDate(LocalDate.of(currentYear, 12, 31));
        }

        // Handle status filters
        if (params.getIsApproved() != null && params.getIsApproved()) {
            params.setStatus(LeaveRequestStatus.APPROVED);
        } else if (params.getIsPending() != null && params.getIsPending()) {
            params.setStatus(LeaveRequestStatus.PENDING);
        } else if (params.getIsRejected() != null && params.getIsRejected()) {
            params.setStatus(LeaveRequestStatus.REJECTED);
        }

        // Use JPA Specifications for filtering
        Specification<LeaveRequest> spec = LeaveRequestSpecification.buildSpecification(params);
        return leaveRequestRepository.findAll(spec, pageable).map(leaveRequestMapper::toDto);
    }

    public int calculateLeaveDays(LocalDate startDate, LocalDate endDate) {
        return (int) startDate.datesUntil(endDate.plusDays(1)).count();
    }

    public Page<LeaveRequestDto> findByManagerParams(UUID userId, ManagerLeaveRequestParams params) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        AdminLeaveRequestParams adminParams = adminLeaveRequestParamsMapper.fromManagerParams(params);
        adminParams.setDepartmentId(employee.getDepartmentId());
        return findByParams(adminParams);
    }

    public Page<LeaveRequestDto> findMyByParams(UUID userId, MyLeaveRequestParams myParams) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));

        // Convert MyLeaveRequestParams to LeaveRequestParams
        AdminLeaveRequestParams params = adminLeaveRequestParamsMapper.fromMyParams(myParams);
        params.setEmployeeId(employee.getId());
        params.setEmployeeCode(employee.getEmployeeCode());
        params.setEmployeeName(employee.getFullName());
        params.setDepartmentId(employee.getDepartmentId());
        return findByParams(params);
    }


    public LeaveThisMonthDto getMyLeaveThisMonth(UUID userId) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        return getLeaveThisMonth(employee.getId());
    }

    public LeaveRequestDto createMyLeaveRequest(UUID userId, EmployeeLeaveRequestCreateDto createDto) {

        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));

        boolean hasOverlap = leaveRequestRepository
            .existsByEmployeeCache_IdAndStatusAndStartDateLessThanEqualAndEndDateGreaterThanEqual(
                employee.getId(), LeaveRequestStatus.APPROVED, createDto.getStartDate(), createDto.getEndDate()
            );

        if (hasOverlap) {
            throw new BadRequestException("Employee already has an approved leave request for this date range");
        }

        LeaveType leaveType = leaveTypeRepository.findById(createDto.getLeaveTypeId()).orElseThrow(() -> new RuntimeException("Leave type not found"));
        LeaveRequest leaveRequest = leaveRequestMapper.toEntity(createDto);
        leaveRequest.setEmployeeCache(employee);
        leaveRequest.setLeaveType(leaveType);
        log.info("Leave request created: {}", leaveRequest);
        leaveRequestRepository.save(leaveRequest);
        return leaveRequestMapper.toDto(leaveRequest);
    }

  public TotalLeaveToday getTotalLeaveToday() {
        TotalLeaveToday totalLeaveToday = new TotalLeaveToday();
        totalLeaveToday.setTotalLeaveToday(leaveRequestRepository.findByStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(LocalDate.now(), LocalDate.now(), LeaveRequestStatus.APPROVED).size());
        totalLeaveToday.setTotalLeaveThisMonth(leaveRequestRepository.findByStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(LocalDate.now().withDayOfMonth(1), LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()), LeaveRequestStatus.APPROVED).size());
        return totalLeaveToday;
  }
}
