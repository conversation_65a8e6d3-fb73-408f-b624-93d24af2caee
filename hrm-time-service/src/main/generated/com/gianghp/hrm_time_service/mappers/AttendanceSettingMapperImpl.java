package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm.dtos.AttendanceSettingBasicDto;
import com.gianghp.hrm_time_service.dtos.get.AttendanceSettingDto;
import com.gianghp.hrm_time_service.dtos.post.AttendanceSettingCreateDto;
import com.gianghp.hrm_time_service.entities.AttendanceSetting;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class AttendanceSettingMapperImpl implements AttendanceSettingMapper {

    @Override
    public AttendanceSettingDto toDto(AttendanceSetting attendanceSetting) {
        if ( attendanceSetting == null ) {
            return null;
        }

        AttendanceSettingDto.AttendanceSettingDtoBuilder attendanceSettingDto = AttendanceSettingDto.builder();

        attendanceSettingDto.id( attendanceSetting.getId() );
        attendanceSettingDto.standardCheckIn( attendanceSetting.getStandardCheckIn() );
        attendanceSettingDto.standardCheckOut( attendanceSetting.getStandardCheckOut() );
        attendanceSettingDto.lateThresholdMinutes( attendanceSetting.getLateThresholdMinutes() );
        attendanceSettingDto.earlyLeaveThresholdMinutes( attendanceSetting.getEarlyLeaveThresholdMinutes() );
        attendanceSettingDto.minWorkingMinutes( attendanceSetting.getMinWorkingMinutes() );
        attendanceSettingDto.standardWorkingHours( attendanceSetting.getStandardWorkingHours() );
        attendanceSettingDto.workType( attendanceSetting.getWorkType() );
        attendanceSettingDto.effectiveDate( attendanceSetting.getEffectiveDate() );
        attendanceSettingDto.createdAt( attendanceSetting.getCreatedAt() );
        attendanceSettingDto.updatedAt( attendanceSetting.getUpdatedAt() );
        attendanceSettingDto.checkInStart( attendanceSetting.getCheckInStart() );
        attendanceSettingDto.checkInEnd( attendanceSetting.getCheckInEnd() );
        attendanceSettingDto.checkOutStart( attendanceSetting.getCheckOutStart() );
        attendanceSettingDto.checkOutEnd( attendanceSetting.getCheckOutEnd() );

        return attendanceSettingDto.build();
    }

    @Override
    public List<AttendanceSettingDto> toDtoList(List<AttendanceSetting> attendanceSettings) {
        if ( attendanceSettings == null ) {
            return null;
        }

        List<AttendanceSettingDto> list = new ArrayList<AttendanceSettingDto>( attendanceSettings.size() );
        for ( AttendanceSetting attendanceSetting : attendanceSettings ) {
            list.add( toDto( attendanceSetting ) );
        }

        return list;
    }

    @Override
    public AttendanceSetting toEntity(AttendanceSettingCreateDto createDto) {
        if ( createDto == null ) {
            return null;
        }

        AttendanceSetting attendanceSetting = new AttendanceSetting();

        attendanceSetting.setStandardCheckIn( createDto.getStandardCheckIn() );
        attendanceSetting.setStandardCheckOut( createDto.getStandardCheckOut() );
        attendanceSetting.setLateThresholdMinutes( createDto.getLateThresholdMinutes() );
        attendanceSetting.setEarlyLeaveThresholdMinutes( createDto.getEarlyLeaveThresholdMinutes() );
        attendanceSetting.setMinWorkingMinutes( createDto.getMinWorkingMinutes() );
        attendanceSetting.setStandardWorkingHours( createDto.getStandardWorkingHours() );
        attendanceSetting.setCheckInStart( createDto.getCheckInStart() );
        attendanceSetting.setCheckInEnd( createDto.getCheckInEnd() );
        attendanceSetting.setCheckOutStart( createDto.getCheckOutStart() );
        attendanceSetting.setCheckOutEnd( createDto.getCheckOutEnd() );
        attendanceSetting.setWorkType( createDto.getWorkType() );
        attendanceSetting.setEffectiveDate( createDto.getEffectiveDate() );
        attendanceSetting.setLateDeductionRate( createDto.getLateDeductionRate() );
        attendanceSetting.setEarlyOutDeductionRate( createDto.getEarlyOutDeductionRate() );
        attendanceSetting.setMinutePerDeduction( createDto.getMinutePerDeduction() );

        return attendanceSetting;
    }

    @Override
    public AttendanceSettingBasicDto toBasicDto(AttendanceSetting attendanceSetting) {
        if ( attendanceSetting == null ) {
            return null;
        }

        AttendanceSettingBasicDto attendanceSettingBasicDto = new AttendanceSettingBasicDto();

        attendanceSettingBasicDto.setId( attendanceSetting.getId() );
        attendanceSettingBasicDto.setMinWorkingMinutes( attendanceSetting.getMinWorkingMinutes() );
        attendanceSettingBasicDto.setStandardWorkingHours( attendanceSetting.getStandardWorkingHours() );
        attendanceSettingBasicDto.setWorkType( attendanceSetting.getWorkType() );
        attendanceSettingBasicDto.setEffectiveDate( attendanceSetting.getEffectiveDate() );

        return attendanceSettingBasicDto;
    }
}
