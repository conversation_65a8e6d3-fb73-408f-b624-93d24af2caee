package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_time_service.dtos.get.AttendanceDto;
import com.gianghp.hrm_time_service.dtos.put.AttendanceUpdateDto;
import com.gianghp.hrm_time_service.entities.Attendance;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class AttendanceMapperImpl implements AttendanceMapper {

    @Override
    public EmployeeCache toEntity(EmployeeBasicDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmployeeCache.EmployeeCacheBuilder employeeCache = EmployeeCache.builder();

        employeeCache.id( dto.getId() );
        employeeCache.userId( dto.getUserId() );
        employeeCache.departmentId( dto.getDepartmentId() );
        employeeCache.employeeCode( dto.getEmployeeCode() );
        employeeCache.fullName( dto.getFullName() );
        employeeCache.departmentName( dto.getDepartmentName() );
        employeeCache.dateOfJoining( dto.getDateOfJoining() );
        employeeCache.designationName( dto.getDesignationName() );

        return employeeCache.build();
    }

    @Override
    public AttendanceDto toDto(Attendance attendance) {
        if ( attendance == null ) {
            return null;
        }

        AttendanceDto.AttendanceDtoBuilder attendanceDto = AttendanceDto.builder();

        attendanceDto.id( attendance.getId() );
        attendanceDto.date( attendance.getDate() );
        attendanceDto.checkIn( attendance.getCheckIn() );
        attendanceDto.checkOut( attendance.getCheckOut() );
        attendanceDto.checkinStatus( attendance.getCheckinStatus() );
        attendanceDto.checkoutStatus( attendance.getCheckoutStatus() );
        attendanceDto.standardWorkingHours( attendance.getStandardWorkingHours() );
        attendanceDto.overtimeHours( attendance.getOvertimeHours() );
        attendanceDto.createdAt( attendance.getCreatedAt() );
        attendanceDto.updatedAt( attendance.getUpdatedAt() );
        if ( attendance.getAutoCheckout() != null ) {
            attendanceDto.autoCheckout( attendance.getAutoCheckout() );
        }

        attendanceDto.employeeId( attendance.getEmployeeCache().getId() );
        attendanceDto.employeeCode( attendance.getEmployeeCache().getEmployeeCode() );
        attendanceDto.fullName( attendance.getEmployeeCache().getFullName() );

        return attendanceDto.build();
    }

    @Override
    public void updateEntity(AttendanceUpdateDto updateDto, Attendance attendance) {
        if ( updateDto == null ) {
            return;
        }

        if ( updateDto.getDate() != null ) {
            attendance.setDate( updateDto.getDate() );
        }
        if ( updateDto.getCheckIn() != null ) {
            attendance.setCheckIn( updateDto.getCheckIn() );
        }
        if ( updateDto.getCheckOut() != null ) {
            attendance.setCheckOut( updateDto.getCheckOut() );
        }
        if ( updateDto.getCheckinStatus() != null ) {
            attendance.setCheckinStatus( updateDto.getCheckinStatus() );
        }
        if ( updateDto.getCheckoutStatus() != null ) {
            attendance.setCheckoutStatus( updateDto.getCheckoutStatus() );
        }
        if ( updateDto.getStandardWorkingHours() != null ) {
            attendance.setStandardWorkingHours( updateDto.getStandardWorkingHours() );
        }
        if ( updateDto.getOvertimeHours() != null ) {
            attendance.setOvertimeHours( updateDto.getOvertimeHours() );
        }
    }

    @Override
    public List<AttendanceDto> toDtoList(List<Attendance> attendances) {
        if ( attendances == null ) {
            return null;
        }

        List<AttendanceDto> list = new ArrayList<AttendanceDto>( attendances.size() );
        for ( Attendance attendance : attendances ) {
            list.add( toDto( attendance ) );
        }

        return list;
    }

    @Override
    public AttendanceBasicDto toBasicDto(Attendance attendance) {
        if ( attendance == null ) {
            return null;
        }

        AttendanceBasicDto.AttendanceBasicDtoBuilder attendanceBasicDto = AttendanceBasicDto.builder();

        attendanceBasicDto.id( attendance.getId() );
        attendanceBasicDto.date( attendance.getDate() );
        attendanceBasicDto.checkinStatus( attendance.getCheckinStatus() );
        attendanceBasicDto.checkoutStatus( attendance.getCheckoutStatus() );
        attendanceBasicDto.standardWorkingHours( attendance.getStandardWorkingHours() );
        attendanceBasicDto.overtimeHours( attendance.getOvertimeHours() );
        attendanceBasicDto.totalLateDeductionRate( attendance.getTotalLateDeductionRate() );
        attendanceBasicDto.totalEarlyOutDeductionRate( attendance.getTotalEarlyOutDeductionRate() );

        attendanceBasicDto.employeeId( attendance.getEmployeeCache().getId() );

        return attendanceBasicDto.build();
    }
}
