package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm.enums.ApproveStatus;
import com.gianghp.hrm_time_service.dtos.get.OvertimeRequestDto;
import com.gianghp.hrm_time_service.dtos.post.EmployeeOvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.OvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.put.OvertimeRequestUpdateDto;
import com.gianghp.hrm_time_service.entities.OvertimeRequest;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class OvertimeRequestMapperImpl implements OvertimeRequestMapper {

    @Override
    public OvertimeRequestDto toDto(OvertimeRequest overtimeRequest) {
        if ( overtimeRequest == null ) {
            return null;
        }

        OvertimeRequestDto.OvertimeRequestDtoBuilder overtimeRequestDto = OvertimeRequestDto.builder();

        overtimeRequestDto.id( overtimeRequest.getId() );
        overtimeRequestDto.date( overtimeRequest.getDate() );
        overtimeRequestDto.totalHours( overtimeRequest.getTotalHours() );
        overtimeRequestDto.reason( overtimeRequest.getReason() );
        overtimeRequestDto.status( overtimeRequest.getStatus() );
        overtimeRequestDto.createdAt( overtimeRequest.getCreatedAt() );
        overtimeRequestDto.updatedAt( overtimeRequest.getUpdatedAt() );

        overtimeRequestDto.employeeId( overtimeRequest.getEmployeeCache().getId() );
        overtimeRequestDto.approverName( overtimeRequest.getApprover() != null ? overtimeRequest.getApprover().getFullName() : null );
        overtimeRequestDto.overtimeSettingId( overtimeRequest.getOvertimeSetting() != null ? overtimeRequest.getOvertimeSetting().getId() : null );
        overtimeRequestDto.employeeName( overtimeRequest.getEmployeeCache().getFullName() );
        overtimeRequestDto.employeeCode( overtimeRequest.getEmployeeCache().getEmployeeCode() );

        return overtimeRequestDto.build();
    }

    @Override
    public List<OvertimeRequestDto> toDtoList(List<OvertimeRequest> overtimeRequests) {
        if ( overtimeRequests == null ) {
            return null;
        }

        List<OvertimeRequestDto> list = new ArrayList<OvertimeRequestDto>( overtimeRequests.size() );
        for ( OvertimeRequest overtimeRequest : overtimeRequests ) {
            list.add( toDto( overtimeRequest ) );
        }

        return list;
    }

    @Override
    public OvertimeRequest toEntity(OvertimeRequestCreateDto createDto) {
        if ( createDto == null ) {
            return null;
        }

        OvertimeRequest overtimeRequest = new OvertimeRequest();

        overtimeRequest.setDate( createDto.getDate() );
        overtimeRequest.setReason( createDto.getReason() );
        overtimeRequest.setTotalHours( createDto.getTotalHours() );

        overtimeRequest.setStatus( ApproveStatus.PENDING );

        return overtimeRequest;
    }

    @Override
    public OvertimeRequest toEntity(EmployeeOvertimeRequestCreateDto dto) {
        if ( dto == null ) {
            return null;
        }

        OvertimeRequest overtimeRequest = new OvertimeRequest();

        overtimeRequest.setDate( dto.getDate() );
        overtimeRequest.setReason( dto.getReason() );
        overtimeRequest.setTotalHours( dto.getTotalHours() );
        overtimeRequest.setStatus( dto.getStatus() );

        return overtimeRequest;
    }

    @Override
    public void updateEntity(OvertimeRequestUpdateDto updateDto, OvertimeRequest overtimeRequest) {
        if ( updateDto == null ) {
            return;
        }

        if ( updateDto.getDate() != null ) {
            overtimeRequest.setDate( updateDto.getDate() );
        }
        if ( updateDto.getReason() != null ) {
            overtimeRequest.setReason( updateDto.getReason() );
        }
        if ( updateDto.getTotalHours() != null ) {
            overtimeRequest.setTotalHours( updateDto.getTotalHours() );
        }
        if ( updateDto.getStatus() != null ) {
            overtimeRequest.setStatus( updateDto.getStatus() );
        }
    }
}
