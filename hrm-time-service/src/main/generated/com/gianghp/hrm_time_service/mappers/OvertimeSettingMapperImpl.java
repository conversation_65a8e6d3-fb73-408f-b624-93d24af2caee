package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.get.OvertimeSettingDto;
import com.gianghp.hrm_time_service.dtos.post.OvertimeSettingCreateDto;
import com.gianghp.hrm_time_service.dtos.put.OvertimeSettingUpdateDto;
import com.gianghp.hrm_time_service.entities.OvertimeSetting;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class OvertimeSettingMapperImpl implements OvertimeSettingMapper {

    @Override
    public OvertimeSettingDto toDto(OvertimeSetting overtimeSetting) {
        if ( overtimeSetting == null ) {
            return null;
        }

        OvertimeSettingDto.OvertimeSettingDtoBuilder overtimeSettingDto = OvertimeSettingDto.builder();

        overtimeSettingDto.id( overtimeSetting.getId() );
        overtimeSettingDto.weekdayRate( overtimeSetting.getWeekdayRate() );
        overtimeSettingDto.holidayRate( overtimeSetting.getHolidayRate() );
        overtimeSettingDto.minimumOvertimeMinutes( overtimeSetting.getMinimumOvertimeMinutes() );
        overtimeSettingDto.maxHoursPerDay( overtimeSetting.getMaxHoursPerDay() );
        overtimeSettingDto.maxHoursPerMonth( overtimeSetting.getMaxHoursPerMonth() );
        overtimeSettingDto.maxDayInThePastToRequest( overtimeSetting.getMaxDayInThePastToRequest() );
        overtimeSettingDto.maxDayInTheFutureToRequest( overtimeSetting.getMaxDayInTheFutureToRequest() );
        overtimeSettingDto.effectiveDate( overtimeSetting.getEffectiveDate() );
        overtimeSettingDto.createdAt( overtimeSetting.getCreatedAt() );
        overtimeSettingDto.updatedAt( overtimeSetting.getUpdatedAt() );

        return overtimeSettingDto.build();
    }

    @Override
    public List<OvertimeSettingDto> toDtoList(List<OvertimeSetting> overtimeSettings) {
        if ( overtimeSettings == null ) {
            return null;
        }

        List<OvertimeSettingDto> list = new ArrayList<OvertimeSettingDto>( overtimeSettings.size() );
        for ( OvertimeSetting overtimeSetting : overtimeSettings ) {
            list.add( toDto( overtimeSetting ) );
        }

        return list;
    }

    @Override
    public OvertimeSetting toEntity(OvertimeSettingCreateDto createDto) {
        if ( createDto == null ) {
            return null;
        }

        OvertimeSetting overtimeSetting = new OvertimeSetting();

        overtimeSetting.setWeekdayRate( createDto.getWeekdayRate() );
        overtimeSetting.setHolidayRate( createDto.getHolidayRate() );
        overtimeSetting.setMinimumOvertimeMinutes( createDto.getMinimumOvertimeMinutes() );
        overtimeSetting.setMaxHoursPerDay( createDto.getMaxHoursPerDay() );
        overtimeSetting.setMaxHoursPerMonth( createDto.getMaxHoursPerMonth() );
        overtimeSetting.setMaxDayInThePastToRequest( createDto.getMaxDayInThePastToRequest() );
        overtimeSetting.setMaxDayInTheFutureToRequest( createDto.getMaxDayInTheFutureToRequest() );
        overtimeSetting.setEffectiveDate( createDto.getEffectiveDate() );

        return overtimeSetting;
    }

    @Override
    public void updateEntity(OvertimeSettingUpdateDto updateDto, OvertimeSetting overtimeSetting) {
        if ( updateDto == null ) {
            return;
        }

        if ( updateDto.getWeekdayRate() != null ) {
            overtimeSetting.setWeekdayRate( updateDto.getWeekdayRate() );
        }
        if ( updateDto.getHolidayRate() != null ) {
            overtimeSetting.setHolidayRate( updateDto.getHolidayRate() );
        }
        overtimeSetting.setMinimumOvertimeMinutes( updateDto.getMinimumOvertimeMinutes() );
        if ( updateDto.getMaxHoursPerDay() != null ) {
            overtimeSetting.setMaxHoursPerDay( updateDto.getMaxHoursPerDay() );
        }
        overtimeSetting.setMaxHoursPerMonth( updateDto.getMaxHoursPerMonth() );
        overtimeSetting.setMaxDayInThePastToRequest( updateDto.getMaxDayInThePastToRequest() );
        overtimeSetting.setMaxDayInTheFutureToRequest( updateDto.getMaxDayInTheFutureToRequest() );
    }
}
