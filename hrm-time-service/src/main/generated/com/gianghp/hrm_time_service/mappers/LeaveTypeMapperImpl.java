package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.get.LeaveTypeDto;
import com.gianghp.hrm_time_service.dtos.post.LeaveTypeCreateDto;
import com.gianghp.hrm_time_service.dtos.put.LeaveTypeUpdateDto;
import com.gianghp.hrm_time_service.entities.LeaveType;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class LeaveTypeMapperImpl implements LeaveTypeMapper {

    @Override
    public LeaveTypeDto toDto(LeaveType leaveType) {
        if ( leaveType == null ) {
            return null;
        }

        LeaveTypeDto.LeaveTypeDtoBuilder leaveTypeDto = LeaveTypeDto.builder();

        leaveTypeDto.active( leaveType.isActive() );
        leaveTypeDto.id( leaveType.getId() );
        leaveTypeDto.name( leaveType.getName() );
        leaveTypeDto.createdAt( leaveType.getCreatedAt() );
        leaveTypeDto.updatedAt( leaveType.getUpdatedAt() );

        return leaveTypeDto.build();
    }

    @Override
    public List<LeaveTypeDto> toDtoList(List<LeaveType> leaveTypes) {
        if ( leaveTypes == null ) {
            return null;
        }

        List<LeaveTypeDto> list = new ArrayList<LeaveTypeDto>( leaveTypes.size() );
        for ( LeaveType leaveType : leaveTypes ) {
            list.add( toDto( leaveType ) );
        }

        return list;
    }

    @Override
    public LeaveType toEntity(LeaveTypeCreateDto createDto) {
        if ( createDto == null ) {
            return null;
        }

        LeaveType leaveType = new LeaveType();

        leaveType.setActive( createDto.isActive() );
        leaveType.setName( createDto.getName() );

        return leaveType;
    }

    @Override
    public void updateEntity(LeaveTypeUpdateDto updateDto, LeaveType leaveType) {
        if ( updateDto == null ) {
            return;
        }

        if ( updateDto.getName() != null ) {
            leaveType.setName( updateDto.getName() );
        }
        leaveType.setActive( updateDto.isActive() );
    }
}
