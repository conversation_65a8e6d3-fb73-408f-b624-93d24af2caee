package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.params.AdminOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyOvertimeRequestParams;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:21+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class AdminOvertimeRequestParamsMapperImpl implements AdminOvertimeRequestParamsMapper {

    @Override
    public AdminOvertimeRequestParams fromMyParams(MyOvertimeRequestParams myParams) {
        if ( myParams == null ) {
            return null;
        }

        AdminOvertimeRequestParams.AdminOvertimeRequestParamsBuilder adminOvertimeRequestParams = AdminOvertimeRequestParams.builder();

        adminOvertimeRequestParams.date( myParams.getDate() );
        adminOvertimeRequestParams.startDate( myParams.getStartDate() );
        adminOvertimeRequestParams.endDate( myParams.getEndDate() );
        adminOvertimeRequestParams.month( myParams.getMonth() );
        adminOvertimeRequestParams.year( myParams.getYear() );
        adminOvertimeRequestParams.minTotalHours( myParams.getMinTotalHours() );
        adminOvertimeRequestParams.maxTotalHours( myParams.getMaxTotalHours() );
        adminOvertimeRequestParams.status( myParams.getStatus() );
        adminOvertimeRequestParams.workType( myParams.getWorkType() );
        adminOvertimeRequestParams.minDuration( myParams.getMinDuration() );
        adminOvertimeRequestParams.maxDuration( myParams.getMaxDuration() );
        adminOvertimeRequestParams.overtimeSettingId( myParams.getOvertimeSettingId() );
        adminOvertimeRequestParams.page( myParams.getPage() );
        adminOvertimeRequestParams.size( myParams.getSize() );
        adminOvertimeRequestParams.sortBy( myParams.getSortBy() );
        adminOvertimeRequestParams.sortDir( myParams.getSortDir() );
        adminOvertimeRequestParams.isApproved( myParams.getIsApproved() );
        adminOvertimeRequestParams.isPending( myParams.getIsPending() );
        adminOvertimeRequestParams.isRejected( myParams.getIsRejected() );
        adminOvertimeRequestParams.isCurrentMonth( myParams.getIsCurrentMonth() );
        adminOvertimeRequestParams.isCurrentYear( myParams.getIsCurrentYear() );
        adminOvertimeRequestParams.isWeekend( myParams.getIsWeekend() );
        adminOvertimeRequestParams.isHoliday( myParams.getIsHoliday() );

        return adminOvertimeRequestParams.build();
    }

    @Override
    public AdminOvertimeRequestParams fromManagerParams(ManagerOvertimeRequestParams managerParams) {
        if ( managerParams == null ) {
            return null;
        }

        AdminOvertimeRequestParams.AdminOvertimeRequestParamsBuilder adminOvertimeRequestParams = AdminOvertimeRequestParams.builder();

        adminOvertimeRequestParams.employeeId( managerParams.getEmployeeId() );
        adminOvertimeRequestParams.employeeCode( managerParams.getEmployeeCode() );
        adminOvertimeRequestParams.employeeName( managerParams.getEmployeeName() );
        adminOvertimeRequestParams.date( managerParams.getDate() );
        adminOvertimeRequestParams.startDate( managerParams.getStartDate() );
        adminOvertimeRequestParams.endDate( managerParams.getEndDate() );
        adminOvertimeRequestParams.month( managerParams.getMonth() );
        adminOvertimeRequestParams.year( managerParams.getYear() );
        adminOvertimeRequestParams.minTotalHours( managerParams.getMinTotalHours() );
        adminOvertimeRequestParams.maxTotalHours( managerParams.getMaxTotalHours() );
        adminOvertimeRequestParams.status( managerParams.getStatus() );
        adminOvertimeRequestParams.workType( managerParams.getWorkType() );
        adminOvertimeRequestParams.minDuration( managerParams.getMinDuration() );
        adminOvertimeRequestParams.maxDuration( managerParams.getMaxDuration() );
        adminOvertimeRequestParams.overtimeSettingId( managerParams.getOvertimeSettingId() );
        adminOvertimeRequestParams.page( managerParams.getPage() );
        adminOvertimeRequestParams.size( managerParams.getSize() );
        adminOvertimeRequestParams.sortBy( managerParams.getSortBy() );
        adminOvertimeRequestParams.sortDir( managerParams.getSortDir() );
        adminOvertimeRequestParams.isApproved( managerParams.getIsApproved() );
        adminOvertimeRequestParams.isPending( managerParams.getIsPending() );
        adminOvertimeRequestParams.isRejected( managerParams.getIsRejected() );
        adminOvertimeRequestParams.isCurrentMonth( managerParams.getIsCurrentMonth() );
        adminOvertimeRequestParams.isCurrentYear( managerParams.getIsCurrentYear() );
        adminOvertimeRequestParams.isWeekend( managerParams.getIsWeekend() );
        adminOvertimeRequestParams.isHoliday( managerParams.getIsHoliday() );

        return adminOvertimeRequestParams.build();
    }
}
