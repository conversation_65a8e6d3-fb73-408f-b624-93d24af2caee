package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.get.HolidayDto;
import com.gianghp.hrm_time_service.dtos.post.HolidayCreateDto;
import com.gianghp.hrm_time_service.entities.Holiday;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class HolidayMapperImpl implements HolidayMapper {

    @Override
    public HolidayDto toDto(Holiday holiday) {
        if ( holiday == null ) {
            return null;
        }

        HolidayDto holidayDto = new HolidayDto();

        holidayDto.setId( holiday.getId() );
        holidayDto.setName( holiday.getName() );
        holidayDto.setDate( holiday.getDate() );
        holidayDto.setHolidayType( holiday.getHolidayType() );
        holidayDto.setDescription( holiday.getDescription() );
        holidayDto.setRecurring( holiday.isRecurring() );

        return holidayDto;
    }

    @Override
    public Holiday toEntity(HolidayCreateDto dto) {
        if ( dto == null ) {
            return null;
        }

        Holiday holiday = new Holiday();

        holiday.setName( dto.getName() );
        holiday.setDate( dto.getDate() );
        holiday.setHolidayType( dto.getHolidayType() );
        holiday.setDescription( dto.getDescription() );
        holiday.setRecurring( dto.isRecurring() );

        return holiday;
    }
}
