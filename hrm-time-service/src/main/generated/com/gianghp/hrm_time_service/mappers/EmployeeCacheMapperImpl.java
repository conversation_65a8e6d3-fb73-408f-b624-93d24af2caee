package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_time_service.dtos.get.EmployeeCacheDto;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class EmployeeCacheMapperImpl implements EmployeeCacheMapper {

    @Override
    public EmployeeCacheDto toDto(EmployeeCache employeeCache) {
        if ( employeeCache == null ) {
            return null;
        }

        EmployeeCacheDto.EmployeeCacheDtoBuilder employeeCacheDto = EmployeeCacheDto.builder();

        employeeCacheDto.id( employeeCache.getId() );
        employeeCacheDto.employeeCode( employeeCache.getEmployeeCode() );
        employeeCacheDto.fullName( employeeCache.getFullName() );
        employeeCacheDto.departmentName( employeeCache.getDepartmentName() );
        employeeCacheDto.workType( employeeCache.getWorkType() );
        employeeCacheDto.updatedAt( employeeCache.getUpdatedAt() );

        return employeeCacheDto.build();
    }

    @Override
    public List<EmployeeCacheDto> toDtoList(List<EmployeeCache> employeeCaches) {
        if ( employeeCaches == null ) {
            return null;
        }

        List<EmployeeCacheDto> list = new ArrayList<EmployeeCacheDto>( employeeCaches.size() );
        for ( EmployeeCache employeeCache : employeeCaches ) {
            list.add( toDto( employeeCache ) );
        }

        return list;
    }

    @Override
    public EmployeeCache toEntity(EmployeeBasicDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmployeeCache.EmployeeCacheBuilder employeeCache = EmployeeCache.builder();

        employeeCache.workType( dto.getType() );
        employeeCache.id( dto.getId() );
        employeeCache.userId( dto.getUserId() );
        employeeCache.departmentId( dto.getDepartmentId() );
        employeeCache.employeeCode( dto.getEmployeeCode() );
        employeeCache.fullName( dto.getFullName() );
        employeeCache.departmentName( dto.getDepartmentName() );
        employeeCache.dateOfJoining( dto.getDateOfJoining() );
        employeeCache.designationName( dto.getDesignationName() );

        return employeeCache.build();
    }

    @Override
    public EmployeeCache toEntity(EmployeeCacheDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmployeeCache.EmployeeCacheBuilder employeeCache = EmployeeCache.builder();

        employeeCache.id( dto.getId() );
        employeeCache.employeeCode( dto.getEmployeeCode() );
        employeeCache.fullName( dto.getFullName() );
        employeeCache.departmentName( dto.getDepartmentName() );
        employeeCache.workType( dto.getWorkType() );

        return employeeCache.build();
    }

    @Override
    public void updateEntity(EmployeeCacheDto dto, EmployeeCache employeeCache) {
        if ( dto == null ) {
            return;
        }

        if ( dto.getEmployeeCode() != null ) {
            employeeCache.setEmployeeCode( dto.getEmployeeCode() );
        }
        if ( dto.getFullName() != null ) {
            employeeCache.setFullName( dto.getFullName() );
        }
        if ( dto.getDepartmentName() != null ) {
            employeeCache.setDepartmentName( dto.getDepartmentName() );
        }
        if ( dto.getWorkType() != null ) {
            employeeCache.setWorkType( dto.getWorkType() );
        }
    }

    @Override
    public void updateEntityFromBasicDto(EmployeeBasicDto dto, EmployeeCache employeeCache) {
        if ( dto == null ) {
            return;
        }

        if ( dto.getUserId() != null ) {
            employeeCache.setUserId( dto.getUserId() );
        }
        if ( dto.getDepartmentId() != null ) {
            employeeCache.setDepartmentId( dto.getDepartmentId() );
        }
        if ( dto.getEmployeeCode() != null ) {
            employeeCache.setEmployeeCode( dto.getEmployeeCode() );
        }
        if ( dto.getFullName() != null ) {
            employeeCache.setFullName( dto.getFullName() );
        }
        if ( dto.getDepartmentName() != null ) {
            employeeCache.setDepartmentName( dto.getDepartmentName() );
        }
        if ( dto.getDateOfJoining() != null ) {
            employeeCache.setDateOfJoining( dto.getDateOfJoining() );
        }
        if ( dto.getDesignationName() != null ) {
            employeeCache.setDesignationName( dto.getDesignationName() );
        }
    }
}
