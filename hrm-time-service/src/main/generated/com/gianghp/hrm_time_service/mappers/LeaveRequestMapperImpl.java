package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.get.LeaveRequestDto;
import com.gianghp.hrm_time_service.dtos.post.EmployeeLeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.LeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.put.LeaveRequestUpdateDto;
import com.gianghp.hrm_time_service.entities.LeaveRequest;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class LeaveRequestMapperImpl implements LeaveRequestMapper {

    @Override
    public LeaveRequestDto toDto(LeaveRequest leaveRequest) {
        if ( leaveRequest == null ) {
            return null;
        }

        LeaveRequestDto.LeaveRequestDtoBuilder leaveRequestDto = LeaveRequestDto.builder();

        leaveRequestDto.id( leaveRequest.getId() );
        leaveRequestDto.startDate( leaveRequest.getStartDate() );
        leaveRequestDto.endDate( leaveRequest.getEndDate() );
        leaveRequestDto.reason( leaveRequest.getReason() );
        leaveRequestDto.numberOfDays( leaveRequest.getNumberOfDays() );
        leaveRequestDto.status( leaveRequest.getStatus() );
        leaveRequestDto.approvedAt( leaveRequest.getApprovedAt() );
        leaveRequestDto.note( leaveRequest.getNote() );
        leaveRequestDto.createdAt( leaveRequest.getCreatedAt() );
        leaveRequestDto.updatedAt( leaveRequest.getUpdatedAt() );

        leaveRequestDto.employeeId( leaveRequest.getEmployeeCache().getId() );
        leaveRequestDto.employeeName( leaveRequest.getEmployeeCache().getFullName() );
        leaveRequestDto.employeeCode( leaveRequest.getEmployeeCache().getEmployeeCode() );
        leaveRequestDto.leaveTypeId( leaveRequest.getLeaveType().getId() );
        leaveRequestDto.approverId( leaveRequest.getApprover() != null ? leaveRequest.getApprover().getId() : null );
        leaveRequestDto.approverName( leaveRequest.getApprover() != null ? leaveRequest.getApprover().getFullName() : null );
        leaveRequestDto.leaveTypeName( leaveRequest.getLeaveType().getName() );

        return leaveRequestDto.build();
    }

    @Override
    public List<LeaveRequestDto> toDtoList(List<LeaveRequest> leaveRequests) {
        if ( leaveRequests == null ) {
            return null;
        }

        List<LeaveRequestDto> list = new ArrayList<LeaveRequestDto>( leaveRequests.size() );
        for ( LeaveRequest leaveRequest : leaveRequests ) {
            list.add( toDto( leaveRequest ) );
        }

        return list;
    }

    @Override
    public LeaveRequest toEntity(LeaveRequestCreateDto createDto) {
        if ( createDto == null ) {
            return null;
        }

        LeaveRequest leaveRequest = new LeaveRequest();

        leaveRequest.setStartDate( createDto.getStartDate() );
        leaveRequest.setEndDate( createDto.getEndDate() );
        leaveRequest.setReason( createDto.getReason() );
        leaveRequest.setStatus( createDto.getStatus() );
        leaveRequest.setApprovedAt( createDto.getApprovedAt() );
        leaveRequest.setNote( createDto.getNote() );

        return leaveRequest;
    }

    @Override
    public LeaveRequest toEntity(EmployeeLeaveRequestCreateDto createDto) {
        if ( createDto == null ) {
            return null;
        }

        LeaveRequest leaveRequest = new LeaveRequest();

        leaveRequest.setStartDate( createDto.getStartDate() );
        leaveRequest.setEndDate( createDto.getEndDate() );
        leaveRequest.setReason( createDto.getReason() );
        leaveRequest.setStatus( createDto.getStatus() );

        return leaveRequest;
    }

    @Override
    public void updateEntity(LeaveRequestUpdateDto updateDto, LeaveRequest leaveRequest) {
        if ( updateDto == null ) {
            return;
        }

        if ( updateDto.getStartDate() != null ) {
            leaveRequest.setStartDate( updateDto.getStartDate() );
        }
        if ( updateDto.getEndDate() != null ) {
            leaveRequest.setEndDate( updateDto.getEndDate() );
        }
        if ( updateDto.getReason() != null ) {
            leaveRequest.setReason( updateDto.getReason() );
        }
        leaveRequest.setNumberOfDays( updateDto.getNumberOfDays() );
        if ( updateDto.getStatus() != null ) {
            leaveRequest.setStatus( updateDto.getStatus() );
        }
        if ( updateDto.getApprovedAt() != null ) {
            leaveRequest.setApprovedAt( updateDto.getApprovedAt() );
        }
        if ( updateDto.getNote() != null ) {
            leaveRequest.setNote( updateDto.getNote() );
        }
    }
}
