package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.params.AdminAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyAttendanceRequestParams;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:22+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class AdminAttendanceRequestParamsMapperImpl implements AdminAttendanceRequestParamsMapper {

    @Override
    public AdminAttendanceRequestParams fromMyParams(MyAttendanceRequestParams myParams) {
        if ( myParams == null ) {
            return null;
        }

        AdminAttendanceRequestParams.AdminAttendanceRequestParamsBuilder adminAttendanceRequestParams = AdminAttendanceRequestParams.builder();

        adminAttendanceRequestParams.date( myParams.getDate() );
        adminAttendanceRequestParams.startDate( myParams.getStartDate() );
        adminAttendanceRequestParams.endDate( myParams.getEndDate() );
        adminAttendanceRequestParams.month( myParams.getMonth() );
        adminAttendanceRequestParams.checkinStatus( myParams.getCheckinStatus() );
        adminAttendanceRequestParams.checkoutStatus( myParams.getCheckoutStatus() );
        adminAttendanceRequestParams.workType( myParams.getWorkType() );
        adminAttendanceRequestParams.page( myParams.getPage() );
        adminAttendanceRequestParams.size( myParams.getSize() );
        adminAttendanceRequestParams.sortBy( myParams.getSortBy() );
        adminAttendanceRequestParams.sortDir( myParams.getSortDir() );

        return adminAttendanceRequestParams.build();
    }

    @Override
    public AdminAttendanceRequestParams fromManagerParams(ManagerAttendanceRequestParams managerParams) {
        if ( managerParams == null ) {
            return null;
        }

        AdminAttendanceRequestParams.AdminAttendanceRequestParamsBuilder adminAttendanceRequestParams = AdminAttendanceRequestParams.builder();

        adminAttendanceRequestParams.employeeId( managerParams.getEmployeeId() );
        adminAttendanceRequestParams.employeeCode( managerParams.getEmployeeCode() );
        adminAttendanceRequestParams.employeeName( managerParams.getEmployeeName() );
        adminAttendanceRequestParams.date( managerParams.getDate() );
        adminAttendanceRequestParams.startDate( managerParams.getStartDate() );
        adminAttendanceRequestParams.endDate( managerParams.getEndDate() );
        adminAttendanceRequestParams.month( managerParams.getMonth() );
        adminAttendanceRequestParams.checkinStatus( managerParams.getCheckinStatus() );
        adminAttendanceRequestParams.checkoutStatus( managerParams.getCheckoutStatus() );
        adminAttendanceRequestParams.workType( managerParams.getWorkType() );
        adminAttendanceRequestParams.page( managerParams.getPage() );
        adminAttendanceRequestParams.size( managerParams.getSize() );
        adminAttendanceRequestParams.sortBy( managerParams.getSortBy() );
        adminAttendanceRequestParams.sortDir( managerParams.getSortDir() );

        return adminAttendanceRequestParams.build();
    }
}
