package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.params.AdminLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyLeaveRequestParams;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T21:38:21+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.1 (Oracle Corporation)"
)
@Component
public class AdminLeaveRequestParamsMapperImpl implements AdminLeaveRequestParamsMapper {

    @Override
    public AdminLeaveRequestParams fromMyParams(MyLeaveRequestParams myParams) {
        if ( myParams == null ) {
            return null;
        }

        AdminLeaveRequestParams.AdminLeaveRequestParamsBuilder adminLeaveRequestParams = AdminLeaveRequestParams.builder();

        adminLeaveRequestParams.startDate( myParams.getStartDate() );
        adminLeaveRequestParams.endDate( myParams.getEndDate() );
        adminLeaveRequestParams.month( myParams.getMonth() );
        adminLeaveRequestParams.year( myParams.getYear() );
        adminLeaveRequestParams.status( myParams.getStatus() );
        adminLeaveRequestParams.leaveTypeId( myParams.getLeaveTypeId() );
        adminLeaveRequestParams.leaveTypeName( myParams.getLeaveTypeName() );
        adminLeaveRequestParams.minDays( myParams.getMinDays() );
        adminLeaveRequestParams.maxDays( myParams.getMaxDays() );
        adminLeaveRequestParams.workType( myParams.getWorkType() );
        adminLeaveRequestParams.page( myParams.getPage() );
        adminLeaveRequestParams.size( myParams.getSize() );
        adminLeaveRequestParams.sortBy( myParams.getSortBy() );
        adminLeaveRequestParams.sortDir( myParams.getSortDir() );
        adminLeaveRequestParams.isApproved( myParams.getIsApproved() );
        adminLeaveRequestParams.isPending( myParams.getIsPending() );
        adminLeaveRequestParams.isRejected( myParams.getIsRejected() );
        adminLeaveRequestParams.isCurrentMonth( myParams.getIsCurrentMonth() );
        adminLeaveRequestParams.isCurrentYear( myParams.getIsCurrentYear() );

        return adminLeaveRequestParams.build();
    }

    @Override
    public AdminLeaveRequestParams fromManagerParams(ManagerLeaveRequestParams managerParams) {
        if ( managerParams == null ) {
            return null;
        }

        AdminLeaveRequestParams.AdminLeaveRequestParamsBuilder adminLeaveRequestParams = AdminLeaveRequestParams.builder();

        adminLeaveRequestParams.employeeId( managerParams.getEmployeeId() );
        adminLeaveRequestParams.employeeCode( managerParams.getEmployeeCode() );
        adminLeaveRequestParams.employeeName( managerParams.getEmployeeName() );
        adminLeaveRequestParams.startDate( managerParams.getStartDate() );
        adminLeaveRequestParams.endDate( managerParams.getEndDate() );
        adminLeaveRequestParams.month( managerParams.getMonth() );
        adminLeaveRequestParams.year( managerParams.getYear() );
        adminLeaveRequestParams.status( managerParams.getStatus() );
        adminLeaveRequestParams.leaveTypeId( managerParams.getLeaveTypeId() );
        adminLeaveRequestParams.leaveTypeName( managerParams.getLeaveTypeName() );
        adminLeaveRequestParams.minDays( managerParams.getMinDays() );
        adminLeaveRequestParams.maxDays( managerParams.getMaxDays() );
        adminLeaveRequestParams.workType( managerParams.getWorkType() );
        adminLeaveRequestParams.page( managerParams.getPage() );
        adminLeaveRequestParams.size( managerParams.getSize() );
        adminLeaveRequestParams.sortBy( managerParams.getSortBy() );
        adminLeaveRequestParams.sortDir( managerParams.getSortDir() );
        adminLeaveRequestParams.isApproved( managerParams.getIsApproved() );
        adminLeaveRequestParams.isPending( managerParams.getIsPending() );
        adminLeaveRequestParams.isRejected( managerParams.getIsRejected() );
        adminLeaveRequestParams.isCurrentMonth( managerParams.getIsCurrentMonth() );
        adminLeaveRequestParams.isCurrentYear( managerParams.getIsCurrentYear() );

        return adminLeaveRequestParams.build();
    }
}
