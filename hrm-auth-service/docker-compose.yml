services:
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: auth-service
    ports:
      - "6010:6010"
    develop:
      watch:
        - action: rebuild
          path: .
    networks:
      - internal
#    labels:
#      - "traefik.enable=true"
#      - "traefik.http.routers.auth.rule=PathPrefix(`/auth-service`)"
#      - "traefik.http.middlewares.auth-stripprefix.stripprefix.prefixes=/auth-service"
#      - "traefik.http.routers.auth.middlewares=auth-stripprefix"
#      - "traefik.http.middlewares.traefik-forward-auth.forwardauth.address=http://auth-service:6010/health"
#      - "traefik.http.middlewares.traefik-forward-auth.forwardauth.authResponseHeaders=X-Auth-Service-Status,X-Checked-By"
#      - "traefik.http.routers.auth.entrypoints=web"
#      - "traefik.http.services.auth.loadbalancer.server.port=6010"
#
#      - "traefik.http.services.auth.loadbalancer.healthcheck.path=/health"
#      - "traefik.http.services.auth.loadbalancer.healthcheck.interval=10s"
#      - "traefik.http.services.auth.loadbalancer.healthcheck.timeout=3s"

networks:
  internal:
    external: true
