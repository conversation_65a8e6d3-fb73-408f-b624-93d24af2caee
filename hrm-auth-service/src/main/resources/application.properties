spring.application.name=hrm-auth-service
spring.config.import=classpath:application-common.properties
server.port=6010

security.config=auth

# Database auth
spring.datasource.url=****************************************
spring.datasource.username=postgres
spring.datasource.password=ggmacket123
spring.jpa.hibernate.ddl-auto=update



# Kafka Configuration
spring.kafka.consumer.group-id=${spring.application.name}-group