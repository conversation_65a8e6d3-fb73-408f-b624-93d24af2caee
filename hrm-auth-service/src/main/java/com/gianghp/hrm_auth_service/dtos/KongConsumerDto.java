package com.gianghp.hrm_auth_service.dtos;

import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class KongConsumerDto {
  private UUID consumerId;
  private String username;
  private JwtCredentialDto jwtCredential;

  @Getter
  @Setter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class JwtCredentialDto {
    private String key;
    private String secret;

  }
}
