package com.gianghp.hrm_auth_service.dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO cho phản hồi verify token - dùng để xác thực token từ request header
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VerifyTokenResponse {
    private boolean valid;
    private UUID userId;
    private String username;
    private String role;
    private LocalDateTime expiresAt;
    private String message;
    
    // Constructor cho token hợp lệ
    public VerifyTokenResponse(boolean valid, UUID userId, String username, String role, LocalDateTime expiresAt) {
        this.valid = valid;
        this.userId = userId;
        this.username = username;
        this.role = role;
        this.expiresAt = expiresAt;
        this.message = valid ? "Token is valid" : "Token is invalid";
    }
    
    // Constructor cho token không hợp lệ
    public VerifyTokenResponse(boolean valid, String message) {
        this.valid = valid;
        this.message = message;
    }
}
