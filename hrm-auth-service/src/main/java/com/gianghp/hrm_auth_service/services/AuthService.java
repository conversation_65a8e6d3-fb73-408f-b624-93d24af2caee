// src/main/java/com/yourcompany/authservice/service/UserService.java
package com.gianghp.hrm_auth_service.services;

import com.gianghp.hrm.enums.RoleType;
import com.gianghp.hrm.security.JwtService;
import com.gianghp.hrm_auth_service.dtos.LoginRequest;
import com.gianghp.hrm_auth_service.dtos.UserDto;
import com.gianghp.hrm_auth_service.dtos.VerifyTokenResponse;
import com.gianghp.hrm_auth_service.entities.User;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import lombok.AllArgsConstructor; // Để Lombok tạo constructor cho dependency injection
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@AllArgsConstructor
@Slf4j
public class AuthService {

    private final AuthenticationManager authenticationManager;
    private final JwtService jwtService;
    private final UserRepository userRepository;

    public Map<String, Object> verify(LoginRequest loginRequest) {
        Map<String, Object> result = new HashMap<>();

        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                loginRequest.getUsername(),
                loginRequest.getPassword()
            )
        );

        log.info("Authentication for user: {}, is Authenticated: {}", loginRequest.getUsername(), authentication.isAuthenticated());

        // Lấy role (giữ nguyên logic của bạn)
        String role = authentication.getAuthorities().stream()
            .findFirst()
            .map(grantedAuthority -> {
                String authority = grantedAuthority.getAuthority();
                return authority.startsWith("ROLE_") ? authority.substring(5) : authority;
            })
            .orElse("USER");

        User account = userRepository.findByUsername(loginRequest.getUsername())
            .orElseThrow(() -> new UsernameNotFoundException("User not found"));

        UserDto user = new UserDto();
        user.setId(account.getId());
        user.setUsername(account.getUsername());
        user.setRole(RoleType.valueOf(role));
        user.setEmail(account.getEmail());
        user.setStatus(account.getStatus());

        result.put("token", jwtService.generateToken(account.getId(), account.getUsername(), role));
        result.put("user", user);
        return result;
    }


    public VerifyTokenResponse verifyToken(String authorizationHeader) {
        if (authorizationHeader == null || !authorizationHeader.startsWith("Bearer ")) {
            return new VerifyTokenResponse(false, "Missing or invalid Authorization header format");
        }

        String token = authorizationHeader.substring(7);
        try {
            // 1. Parse và verify một lần, lấy claims
            Claims claims = jwtService.extractAllClaims(token); // đồng thời kiểm tra signature
            Date expiration = claims.getExpiration();
            if (expiration.before(new Date())) {
                return new VerifyTokenResponse(false, "Token is expired");
            }

            // 2. Lấy thông tin cần thiết từ claims
            UUID userId = UUID.fromString(claims.getSubject());
            String username = claims.get("username", String.class);
            String role = claims.get("role", String.class);

            LocalDateTime expiresAt = expiration.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

            log.info("Token verified successfully for user: {}, role: {}", username, role);

            return new VerifyTokenResponse(true, userId, username, role, expiresAt);
        } catch (JwtException | IllegalArgumentException e) {
            log.error("Token verification failed: {}", e.getMessage());
            return new VerifyTokenResponse(false, "Invalid token: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during token verification: {}", e.getMessage(), e);
            return new VerifyTokenResponse(false, "Token verification failed");
        }
    }


}