package com.gianghp.hrm_auth_service.services;

import com.gianghp.hrm.dtos.BaseSalaryBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.enums.CurrencyCode;
import com.gianghp.hrm.enums.Gender;
import com.gianghp.hrm.enums.RoleType;
import com.gianghp.hrm.enums.WorkLocationType;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_auth_service.clients.KongClient;
import com.gianghp.hrm_auth_service.dtos.CreateUserRequestDto;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * User Management Service - <PERSON><PERSON> lý việc tạo và quản lý user
 * <PERSON><PERSON> thể được gọi thông qua API hoặc các service khác
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserManagementService {

    private final UserRepository userRepository;
    private final UserService userService;
    private final KongClient kongClient;

    /**
     * Tạo default users (admin, manager)
     */
    public void createDefaultUsers() {
        log.info("Creating default system users...");
        
        // Create default admin and manager users
        createUserIfNotExists("admin", "<EMAIL>", "admin123", RoleType.ADMIN);
        createUserIfNotExists("manager", "<EMAIL>", "manager123", RoleType.MANAGER);

        log.info("=== Default Login Credentials ===");
        log.info("Admin: username=admin, password=admin123");
        log.info("Manager: username=manager, password=manager123");
        log.info("================================");
    }

    /**
     * Tạo user nếu chưa tồn tại
     */
    public void createUserIfNotExists(String username, String email, String password, RoleType roleType) {
        try {
            if (userRepository.findByUsername(username).isPresent()) {
                log.info("User {} already exists, skipping creation", username);
                return;
            }

            CreateUserRequestDto request = new CreateUserRequestDto();
            request.setUsername(username);
            request.setEmail(email);
            request.setPassword(password);
            request.setRole(roleType);

            userService.createUser(request);
            log.info("Created default {} user: {}", roleType, username);
        } catch (Exception e) {
            log.error("Failed to create default user {}: {}", username, e.getMessage(), e);
        }
    }

    /**
     * Tạo sample employee users với Kafka integration
     */
    public void createSampleEmployeeUsers() throws IOException, InterruptedException {
        log.info("Creating sample employee users with Kafka integration...");

        LocalDate today = LocalDate.now();

        List<CreateUserRequestDto> employeeRequests = createSampleEmployeeRequests(today);

        int created = 0;
        int skipped = 0;
        int failed = 0;

        for (CreateUserRequestDto request : employeeRequests) {
            try {
                if (!userRepository.existsByUsername(request.getUsername())) {
                    userService.createUser(request); // This will trigger Kafka producer
                    log.debug("Created employee user: {} with Kafka event", request.getUsername());
                    created++;
                } else {
                    log.debug("Employee user {} already exists, skipping", request.getUsername());
                    skipped++;
                }
            } catch (Exception e) {
                log.error("Failed to create employee user {}: {}", request.getUsername(), e.getMessage());
                kongClient.deleteConsumer(request.getUsername());
                failed++;
            }
        }

        log.info("Sample employee users creation completed - Created: {}, Skipped: {}, Failed: {}", 
                created, skipped, failed);
    }

    /**
     * Tạo danh sách sample employee requests
     */
    private List<CreateUserRequestDto> createSampleEmployeeRequests(LocalDate today) {
        return List.of(
            // --- Information Technology ---
            createEmployeeRequest("emp001", "<EMAIL>", "Alice", "Nguyen",
                "Information Technology", "Software Engineer",
                UUID.fromString("f360a19c-d2c8-4a7a-a067-015091021081"),
                UUID.fromString("696e0557-9105-4630-931f-d94cb152673f"),
                new BigDecimal("12000000"), today),
            createEmployeeRequest("emp002", "<EMAIL>", "Bob", "Tran",
                "Information Technology", "Senior Software Engineer",
                UUID.fromString("f360a19c-d2c8-4a7a-a067-015091021081"),
                UUID.fromString("f8e67d76-15f8-4e60-9d25-b361bc654315"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp003", "<EMAIL>", "Carol", "Pham",
                "Information Technology", "Tech Lead",
                UUID.fromString("f360a19c-d2c8-4a7a-a067-015091021081"),
                UUID.fromString("43197333-324a-4e9a-a57c-72d3226abc2a"),
                new BigDecimal("18000000"), today),
            createEmployeeRequest("emp004", "<EMAIL>", "David", "Le",
                "Information Technology", "DevOps Engineer",
                UUID.fromString("f360a19c-d2c8-4a7a-a067-015091021081"),
                UUID.fromString("52bdf0fd-fb99-4c5a-8d8e-05dd3d7d5a25"),
                new BigDecimal("14000000"), today),
            createEmployeeRequest("emp005", "<EMAIL>", "Eva", "Ho",
                "Information Technology", "QA Engineer",
                UUID.fromString("f360a19c-d2c8-4a7a-a067-015091021081"),
                UUID.fromString("f50afac2-1d66-4ec3-b9d5-ade051b05f9a"),
                new BigDecimal("********"), today),

            // --- Human Resources ---
            createEmployeeRequest("emp006", "<EMAIL>", "Fiona", "Dang",
                "Human Resources", "HR Manager",
                UUID.fromString("37ddcbe1-4b3e-4d6c-882d-7e93731c45f5"),
                UUID.fromString("fc35c43f-405f-4815-bd2b-80dfaa7475c6"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp007", "<EMAIL>", "George", "Vo",
                "Human Resources", "HR Specialist",
                UUID.fromString("37ddcbe1-4b3e-4d6c-882d-7e93731c45f5"),
                UUID.fromString("94f87469-d1ce-4278-adb5-c1ccc2616059"),
                new BigDecimal("12000000"), today),
            createEmployeeRequest("emp008", "<EMAIL>", "Helen", "Nguyen",
                "Human Resources", "Recruiter",
                UUID.fromString("37ddcbe1-4b3e-4d6c-882d-7e93731c45f5"),
                UUID.fromString("982f3ff7-f567-4596-bec8-94e7d6ce61de"),
                new BigDecimal("11000000"), today),
            createEmployeeRequest("emp009", "<EMAIL>", "Ian", "Pham",
                "Human Resources", "Operations Manager",
                UUID.fromString("37ddcbe1-4b3e-4d6c-882d-7e93731c45f5"),
                UUID.fromString("4a34e1ee-425b-4058-82d2-6f53355adf53"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp010", "<EMAIL>", "Julia", "Le",
                "Human Resources", "Project Manager",
                UUID.fromString("37ddcbe1-4b3e-4d6c-882d-7e93731c45f5"),
                UUID.fromString("8794f2ae-ee95-4b89-b3e0-42f861e2f64f"),
                new BigDecimal("********"), today),

            // --- Finance & Accounting ---
            createEmployeeRequest("emp011", "<EMAIL>", "Kevin", "Nguyen",
                "Finance & Accounting", "Finance Manager",
                UUID.fromString("40625e4d-c4d0-4b1d-9fcd-bf6bf1e7e212"),
                UUID.fromString("d32e062c-4d7a-44e2-96a6-b713d31af372"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp012", "<EMAIL>", "Lisa", "Tran",
                "Finance & Accounting", "Accountant",
                UUID.fromString("40625e4d-c4d0-4b1d-9fcd-bf6bf1e7e212"),
                UUID.fromString("e0c6dd0b-0904-4903-b34a-45bfc4f7189a"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp013", "<EMAIL>", "Michael", "Hoang",
                "Finance & Accounting", "Financial Analyst",
                UUID.fromString("40625e4d-c4d0-4b1d-9fcd-bf6bf1e7e212"),
                UUID.fromString("700a70fd-db75-46b2-a679-36868914d190"),
                new BigDecimal("********"), today),

            // --- Marketing & Sales ---
            createEmployeeRequest("emp014", "<EMAIL>", "Nina", "Vo",
                "Marketing & Sales", "Marketing Manager",
                UUID.fromString("d4278d0a-3748-42d3-950d-77c27ab56f6a"),
                UUID.fromString("80620a1b-ebdb-43a7-9bfd-b2204fa02ca8"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp015", "<EMAIL>", "Oliver", "Le",
                "Marketing & Sales", "Sales Representative",
                UUID.fromString("d4278d0a-3748-42d3-950d-77c27ab56f6a"),
                UUID.fromString("b2f33945-976e-4b3a-87e2-0b8cda465d93"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp016", "<EMAIL>", "Patty", "Dang",
                "Marketing & Sales", "Digital Marketing Specialist",
                UUID.fromString("d4278d0a-3748-42d3-950d-77c27ab56f6a"),
                UUID.fromString("d56a11a3-d44d-4702-8141-6edb47425fa4"),
                new BigDecimal("13500000"), today),

            // --- Operations ---
            createEmployeeRequest("emp017", "<EMAIL>", "Quinn", "Nguyen",
                "Operations", "Operations Manager",
                UUID.fromString("fe883a26-76a6-405a-884e-cefd578fe054"),
                UUID.fromString("4a34e1ee-425b-4058-82d2-6f53355adf53"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp018", "<EMAIL>", "Ryan", "Pham",
                "Operations", "Project Manager",
                UUID.fromString("fe883a26-76a6-405a-884e-cefd578fe054"),
                UUID.fromString("8794f2ae-ee95-4b89-b3e0-42f861e2f64f"),
                new BigDecimal("********"), today),

            // --- Quality Assurance ---
            createEmployeeRequest("emp019", "<EMAIL>", "Susan", "Le",
                "Quality Assurance", "QA Engineer",
                UUID.fromString("9a8ad8f3-5033-4b8a-9ac9-1ab5251514f1"),
                UUID.fromString("f50afac2-1d66-4ec3-b9d5-ade051b05f9a"),
                new BigDecimal("********"), today),
            createEmployeeRequest("emp020", "<EMAIL>", "Thomas", "Tran",
                "Quality Assurance", "Team Lead",
                UUID.fromString("9a8ad8f3-5033-4b8a-9ac9-1ab5251514f1"),
                UUID.fromString("09d47197-d1d8-4bfa-92f5-9c01da2665c8"),
                new BigDecimal("14000000"), today)
        );
    }



    /**
     * Tạo employee request với thông tin chi tiết
     */
    private CreateUserRequestDto createEmployeeRequest(String username, String email,
                                                       String firstName, String lastName, String department, String designation, UUID departmentId,
                                                       UUID designationId, BigDecimal baseSalary, LocalDate effectiveDate) {
        CreateUserRequestDto request = new CreateUserRequestDto();
        request.setUsername(username);
        request.setEmail(email);
        request.setPassword("employee123");

        int empNum = Integer.parseInt(username.substring(3, 6));

        EmployeeBasicDto employee = EmployeeBasicDto.builder()
                .employeeCode(username.toUpperCase())
                .firstName(firstName)
                .lastName(lastName)
                .email(email)
                .mobileNumber("+1234567" + String.format("%03d", empNum))
                .dateOfBirth(LocalDate.of(1990 + (empNum % 10),
                        Math.max(1, (empNum % 12) + 1),
                        Math.max(1, (empNum % 28) + 1)))
                .gender(empNum % 2 == 0 ? Gender.MALE : Gender.FEMALE)
                .nationality("American")
                .address("123 " + department + " Street, City")
                .city("New York")
                .state("NY")
                .dateOfJoining(LocalDate.now())
                .workLocationType(WorkLocationType.ON_SITE)
                .type(WorkType.FULL_TIME)
                .departmentName(department)
                .designationName(designation)
                .departmentId(departmentId)
                .designationId(designationId)
                .build();
        
        employee.setBaseSalary(BaseSalaryBasicDto.builder()
                .amount(baseSalary)
                .effectiveDate(effectiveDate)
                .build());

        request.setEmployee(employee);
        return request;
    }
}
