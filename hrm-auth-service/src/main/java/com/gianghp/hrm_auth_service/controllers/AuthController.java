package com.gianghp.hrm_auth_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_auth_service.dtos.*;
import com.gianghp.hrm_auth_service.models.UserPrincipal;
import com.gianghp.hrm_auth_service.services.AuthService;
import com.gianghp.hrm_auth_service.services.PasswordService;
import jakarta.validation.Valid;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;
    private final PasswordService passwordService;




    @GetMapping("/me")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> userInfo = new HashMap<>();

        if (authentication != null && authentication.isAuthenticated()) {
            Object principal = authentication.getPrincipal();

            // Handle UserPrincipal (from local authentication)
            if (principal instanceof UserPrincipal userPrincipal) {
                userInfo.put("id", userPrincipal.getUser().getId());
                userInfo.put("username", userPrincipal.getUsername());
                userInfo.put("email", userPrincipal.getUser().getEmail());
                userInfo.put("role", userPrincipal.getUser().getRole().getName());
                userInfo.put("status", userPrincipal.getUser().getStatus());
                userInfo.put("authorities", userPrincipal.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toList()));
            }
            // Handle User (from JWT token validation)
            else if (principal instanceof UserDetails userDetails) {
                userInfo.put("username", userDetails.getUsername());
                userInfo.put("authorities", userDetails.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toList()));

                // Extract role from authorities
                String role = userDetails.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .filter(auth -> auth.startsWith("ROLE_"))
                        .map(auth -> auth.substring(5)) // Remove "ROLE_" prefix
                        .findFirst()
                        .orElse("UNKNOWN");
                userInfo.put("role", role);
                userInfo.put("source", "JWT_TOKEN");
            }

            return ResponseEntity.ok(ApiResponse.success("User information retrieved", userInfo));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("User not authenticated"));
        }
    }

    @PutMapping("/change-password")
    public ResponseEntity<ApiResponse<String>> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        try {
            passwordService.changePassword(request);
            return ResponseEntity.ok(ApiResponse.success("Password changed successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to change password: " + e.getMessage()));
        }
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<ApiResponse<Map<String, String>>> forgotPassword(@Valid @RequestBody ForgotPasswordRequest request) {
        try {
            String token = passwordService.forgotPassword(request);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Password reset instructions sent to your email");
            response.put("token", token); // For testing purposes only
            
            return ResponseEntity.ok(ApiResponse.success("Reset token generated", response));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to process forgot password request: " + e.getMessage()));
        }
    }

    @PutMapping("/reset-password")
    public ResponseEntity<ApiResponse<String>> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        try {
            passwordService.resetPassword(request);
            return ResponseEntity.ok(ApiResponse.success("Password reset successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to reset password: " + e.getMessage()));
        }
    }
}
