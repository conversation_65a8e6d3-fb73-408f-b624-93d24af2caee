package com.gianghp.hrm_auth_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_auth_service.services.UserManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Data Initialization Controller - Cung cấp các API để khởi tạo dữ liệu
 * Chỉ admin mới có thể sử dụng các API này
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/data-init")
@Tag(name = "Data Initialization", description = "APIs for initializing system data")
@Slf4j
public class DataInitializationController {

    private final UserManagementService userManagementService;

    /**
     * API để tạo sample employee users
     */
    @PostMapping("/sample-employees")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Create sample employees", description = "Creates sample employee users with Kafka integration")
    public ResponseEntity<ApiResponse<String>> createSampleEmployees() {
        try {
            log.info("API call: Creating sample employee users...");
            userManagementService.createSampleEmployeeUsers();
            return ResponseEntity.ok(ApiResponse.success("Sample employee users created successfully", null));
        } catch (Exception e) {
            log.error("Failed to create sample employees via API: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create sample employees: " + e.getMessage()));
        }
    }


}
