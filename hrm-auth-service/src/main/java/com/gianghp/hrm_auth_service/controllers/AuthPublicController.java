package com.gianghp.hrm_auth_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_auth_service.dtos.LoginRequest;
import com.gianghp.hrm_auth_service.dtos.LoginResponse;
import com.gianghp.hrm_auth_service.dtos.UserDto;
import com.gianghp.hrm_auth_service.services.AuthService;
import com.gianghp.hrm_auth_service.services.PasswordService;
import jakarta.validation.Valid;
import java.time.Duration;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/public/auth")
@RequiredArgsConstructor
public class AuthPublicController {
  private final AuthService authService;

  @PostMapping("/login")
  public ResponseEntity<ApiResponse<UserDto>> login(@Valid @RequestBody LoginRequest loginRequest) {
    try {
      Map<String, Object> result = authService.verify(loginRequest);
      if (result.containsKey("error")) {
        return ResponseEntity.badRequest()
            .body(ApiResponse.error("Invalid username or password"));
      }

      UserDto response = (UserDto) result.get("user");

      // Set JWT vào HttpOnly Cookie
      ResponseCookie cookie = ResponseCookie.from("access_token", result.get("token").toString())
          .httpOnly(true)// => đặt false nếu đang test local HTTP
          .secure(false)
          .sameSite("Lax")
          .path("/")
          .maxAge(Duration.ofMinutes(10000000))
          .build();

      return ResponseEntity.ok()
          .header(HttpHeaders.SET_COOKIE, cookie.toString())
          .body(ApiResponse.success("Login successful", response));

    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Login failed: " + e.getMessage()));
    }
  }
}
