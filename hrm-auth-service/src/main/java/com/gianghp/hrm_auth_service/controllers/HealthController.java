package com.gianghp.hrm_auth_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_auth_service.clients.KongClient;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/public/health")
@RequiredArgsConstructor
@Slf4j
public class HealthController {
  private final KongClient kongClient;
  private final UserRepository userRepository;

  @GetMapping
  public String health() {
    return "AUTH OK";
  }

  @PostMapping("/test")
  public ResponseEntity<ApiResponse<String>> test() {
    try {
      kongClient.createConsumer(userRepository.findAll().getFirst());
    } catch (Exception e) {
      log.error("Failed to create consumer: {}", e.getMessage());
    }
    return ResponseEntity.ok(ApiResponse.success("Test successful", null));
  }
}
