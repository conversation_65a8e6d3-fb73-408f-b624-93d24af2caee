package com.gianghp.hrm_auth_service.acl;

import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import com.gianghp.hrm_auth_service.clients.KongClient;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserACL {
    private final UserRepository userRepository;
    private final KongClient kongClient;

    public void handleEmployeeCreatedEventFailed(UUID userId) {
        userRepository.deleteById(userId);
        log.info("User deleted successfully");
        try {
            kongClient.deleteConsumer(userId.toString());
        } catch (Exception e) {
            log.error("Failed to delete consumer: {}", e.getMessage());
        }
    }
}
