package com.gianghp.hrm_auth_service.clients;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.gianghp.hrm.configs.JwtConfig;
import com.gianghp.hrm_auth_service.entities.User;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Base64;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class KongClient {
  private final JwtConfig jwtConfig;
  private final HttpClient httpClient;
  private final ObjectMapper mapper;

  // Tạo consumer (theo username, bạn có thể dùng user_id hoặc email)
  public void createConsumer(User user) throws IOException, InterruptedException {
    // 1️⃣ Tạo consumer
    ObjectNode consumerBody = mapper.createObjectNode();
    consumerBody.put("username", user.getUsername());
    consumerBody.put("custom_id", String.valueOf(user.getId()));

    HttpRequest createConsumerReq = HttpRequest.newBuilder()
        .uri(URI.create(jwtConfig.getKongAdminBaseUrl() + "/consumers"))
        .header("Accept", "application/json")
        .header("Content-Type", "application/json")
        .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(consumerBody)))
        .build();

    HttpResponse<String> consumerResp = httpClient.send(createConsumerReq, HttpResponse.BodyHandlers.ofString());
    if (consumerResp.statusCode() != 201 && consumerResp.statusCode() != 200) {
      throw new RuntimeException(
          "Failed to create consumer: " + consumerResp.statusCode() + " " + consumerResp.body());
    }

    // 2️⃣ Tạo JWT credential cho consumer
    ObjectNode jwtBody = mapper.createObjectNode();
    jwtBody.put("algorithm", "HS256");
    jwtBody.put("key", String.valueOf(user.getId()));

    String secretBase64 = Base64.getEncoder()
        .encodeToString(jwtConfig.getKey().getEncoded());
    jwtBody.put("secret", secretBase64);

    HttpRequest createJwtReq = HttpRequest.newBuilder()
        .uri(URI.create(jwtConfig.getKongAdminBaseUrl() + "/consumers/" + user.getUsername() + "/jwt"))
        .header("Accept", "application/json")
        .header("Content-Type", "application/json")
        .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(jwtBody)))
        .build();

    HttpResponse<String> jwtResp = httpClient.send(createJwtReq, HttpResponse.BodyHandlers.ofString());
    if (jwtResp.statusCode() != 201 && jwtResp.statusCode() != 200) {
      throw new RuntimeException(
          "Failed to create JWT credential: " + jwtResp.statusCode() + " " + jwtResp.body());
    }

    log.info("Successfully created consumer '{}' with JWT credential", user.getUsername());
  }

  public void deleteConsumer(String userId) throws IOException, InterruptedException {
    HttpRequest deleteReq = HttpRequest.newBuilder()
        .uri(URI.create(jwtConfig.getKongAdminBaseUrl() + "/consumers/" + userId))
        .DELETE()
        .build();

    HttpResponse<String> response = httpClient.send(deleteReq, HttpResponse.BodyHandlers.ofString());
    if (response.statusCode() != 204 && response.statusCode() != 200) {
      throw new RuntimeException("Failed to delete consumer '" + userId + "': " + response.statusCode() + " " + response.body());
    }

    log.info("Successfully deleted consumer '{}'", userId);
  }


}
