package com.gianghp.hrm_auth_service.configs;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm.enums.RoleType;
import com.gianghp.hrm_auth_service.entities.Permission;
import com.gianghp.hrm_auth_service.entities.Role;
import com.gianghp.hrm_auth_service.entities.RolePermission;
import com.gianghp.hrm_auth_service.repositories.PermissionRepository;
import com.gianghp.hrm_auth_service.repositories.RolePermissionRepository;
import com.gianghp.hrm_auth_service.repositories.RoleRepository;
import com.gianghp.hrm_auth_service.services.UserManagementService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * System Initializer Config - Khởi tạo dữ liệu hệ thống cơ bản khi ứng dụng khởi động
 * Chỉ chạy một lần khi khởi động để tạo roles, permissions, role-permissions
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!test") // Không chạy trong test environment
public class SystemInitializerConfig implements CommandLineRunner {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final UserManagementService userManagementService;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Starting System Initialization ===");
        
        initRoles();
        initPermissions();
        initRolePermissions();

        createDefaultUsers();
        
        log.info("=== System Initialization Completed ===");
    }

    public void createDefaultUsers() {
        try {
            log.info("API call: Creating default users...");
            userManagementService.createDefaultUsers();
        } catch (Exception e) {
            log.error("Failed to create default users via API: {}", e.getMessage());
        }
    }

    /**
     * Khởi tạo các role cơ bản của hệ thống
     */
    private void initRoles() {
        log.info("Initializing system roles...");
        
        for (RoleType roleType : RoleType.values()) {
            if (roleRepository.findByName(roleType).isEmpty()) {
                Role role = new Role();
                role.setName(roleType);
                role.setDescription(roleType.name() + " role");
                roleRepository.save(role);
                log.info("Created role: {}", roleType);
            } else {
                log.debug("Role {} already exists", roleType);
            }
        }
        
        log.info("System roles initialization completed");
    }

    /**
     * Khởi tạo các permission cơ bản của hệ thống
     */
    private void initPermissions() {
        if (permissionRepository.count() > 0) {
            log.info("Permissions already exist, skipping initialization");
            return;
        }
        
        log.info("Initializing system permissions...");

        // User management permissions
        createPermissionIfNotExists("USER_READ", "Read user information");
        createPermissionIfNotExists("USER_WRITE", "Create and update users");
        createPermissionIfNotExists("USER_DELETE", "Delete users");
        createPermissionIfNotExists("USER_MANAGE_ROLES", "Manage user roles");

        // Leave management permissions
        createPermissionIfNotExists("LEAVE_REQUEST_VIEW", "View leave requests");
        createPermissionIfNotExists("LEAVE_REQUEST_CREATE", "Create leave requests");
        createPermissionIfNotExists("LEAVE_REQUEST_APPROVE", "Approve leave requests");

        // Payroll permissions
        createPermissionIfNotExists("PAYROLL_VIEW", "View payroll information");
        createPermissionIfNotExists("PAYROLL_PROCESS", "Process payroll");

        // System permissions
        createPermissionIfNotExists("SYSTEM_ADMIN", "Full system administration");

        log.info("System permissions initialization completed");
    }

    /**
     * Tạo permission nếu chưa tồn tại
     */
    private void createPermissionIfNotExists(String name, String description) {
        if (permissionRepository.findByName(name).isEmpty()) {
            Permission permission = new Permission();
            permission.setName(name);
            permission.setDescription(description);
            permissionRepository.save(permission);
            log.debug("Created permission: {}", name);
        }
    }

    /**
     * Khởi tạo mối quan hệ role-permission
     */
    private void initRolePermissions() {
        log.info("Initializing role permissions...");

        // ADMIN - Full access
        assignPermissionsToRole(RoleType.ADMIN,
                "SYSTEM_ADMIN", "USER_READ", "USER_WRITE", "USER_DELETE", "USER_MANAGE_ROLES",
                "LEAVE_REQUEST_VIEW", "LEAVE_REQUEST_CREATE", "LEAVE_REQUEST_APPROVE",
                "PAYROLL_VIEW", "PAYROLL_PROCESS");

        // MANAGER - Management permissions
        assignPermissionsToRole(RoleType.MANAGER,
                "USER_READ", "LEAVE_REQUEST_VIEW", "LEAVE_REQUEST_APPROVE",
                "PAYROLL_VIEW");

        // EMPLOYEE - Basic permissions
        assignPermissionsToRole(RoleType.EMPLOYEE,
                "LEAVE_REQUEST_VIEW", "LEAVE_REQUEST_CREATE");

        log.info("Role permissions initialization completed");
    }

    /**
     * Gán permissions cho role
     */
    private void assignPermissionsToRole(RoleType roleType, String... permissionNames) {
        Role role = roleRepository.findByName(roleType).orElse(null);
        if (role == null) {
            log.warn("Role {} not found, skipping permission assignment", roleType);
            return;
        }

        for (String permissionName : permissionNames) {
            Permission permission = permissionRepository.findByName(permissionName).orElse(null);
            if (permission == null) {
                log.warn("Permission {} not found, skipping", permissionName);
                continue;
            }

            // Check if role-permission relationship already exists
            if (rolePermissionRepository.findByRoleAndPermission(role, permission).isEmpty()) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setRole(role);
                rolePermission.setPermission(permission);
                rolePermissionRepository.save(rolePermission);
                log.debug("Assigned permission {} to role {}", permissionName, roleType);
            }
        }
    }
}
