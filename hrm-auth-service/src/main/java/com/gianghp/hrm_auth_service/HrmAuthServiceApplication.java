package com.gianghp.hrm_auth_service;

import com.gianghp.hrm.configs.SimpleSwaggerConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Import({SimpleSwaggerConfig.class})
@SpringBootApplication
@EnableJpaRepositories("com.gianghp.hrm_auth_service.repositories")
@EntityScan("com.gianghp.hrm_auth_service.entities")
@ComponentScan(basePackages = {
    "com.gianghp.hrm_auth_service",
    "com.gianghp.hrm" // Scan hrm-common components
})
public class HrmAuthServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(HrmAuthServiceApplication.class, args);
	}


}
