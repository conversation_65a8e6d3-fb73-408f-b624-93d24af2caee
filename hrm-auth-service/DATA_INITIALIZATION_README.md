# Data Initialization System

Hệ thống khởi tạo dữ liệu đã được tách thành hai phần chính:

## 1. SystemInitializerConfig (Tự động khởi tạo)

**File:** `src/main/java/com/gianghp/hrm_auth_service/configs/SystemInitializerConfig.java`

**Chức năng:**
- Tự động chạy khi ứng dụng khởi động
- Khởi tạo các dữ liệu hệ thống cơ bản:
  - **Roles**: ADMIN, MANAGER, EMPLOYEE
  - **Permissions**: USER_READ, USER_WRITE, LEAVE_REQUEST_VIEW, etc.
  - **Role-Permission mappings**: Gán quyền cho từng role

**Đặc điểm:**
- Chỉ chạy một lần khi khởi động
- Không chạy trong test environment (`@Profile("!test")`)
- Tự động kiểm tra và chỉ tạo dữ liệu chưa tồn tại

## 2. UserManagementService + DataInitializationController (API)

**Files:**
- `src/main/java/com/gianghp/hrm_auth_service/services/UserManagementService.java`
- `src/main/java/com/gianghp/hrm_auth_service/controllers/DataInitializationController.java`

**Chức năng:**
- Cung cấp API để tạo users thông qua HTTP requests
- Có thể gọi từ Swagger UI hoặc các client khác

## API Endpoints

### Base URL: `/api/admin/data-init`

**Lưu ý:** Tất cả API đều yêu cầu quyền ADMIN (`@PreAuthorize("hasRole('ADMIN')")`)

### 1. Tạo Default Users
```http
POST /api/admin/data-init/default-users
```

**Mô tả:** Tạo admin và manager users mặc định

**Response:**
```json
{
  "success": true,
  "message": "Default users created successfully",
  "data": null
}
```

**Default Credentials:**
- Admin: `username=admin, password=admin123`
- Manager: `username=manager, password=manager123`

### 2. Tạo Sample Employees
```http
POST /api/admin/data-init/sample-employees
```

**Mô tả:** Tạo các employee users mẫu với Kafka integration

**Response:**
```json
{
  "success": true,
  "message": "Sample employee users created successfully",
  "data": null
}
```

**Sample Employees:**
- emp001-emp010: Các employees từ các department khác nhau
- Password: `employee123`

### 3. Tạo Tất Cả Dữ Liệu Mẫu
```http
POST /api/admin/data-init/all-sample-data
```

**Mô tả:** Tạo tất cả dữ liệu mẫu (default users + sample employees)

## Quy Trình Khởi Tạo Dữ Liệu

### Lần Đầu Khởi Động Ứng Dụng:

1. **Tự động:** `SystemInitializerConfig` sẽ tạo roles, permissions, role-permissions
2. **Thủ công:** Gọi API để tạo users:
   ```bash
   # Tạo default users
   curl -X POST http://localhost:8080/api/admin/data-init/default-users \
        -H "Authorization: Bearer <admin-token>"
   
   # Hoặc tạo tất cả dữ liệu mẫu
   curl -X POST http://localhost:8080/api/admin/data-init/all-sample-data \
        -H "Authorization: Bearer <admin-token>"
   ```

### Sử Dụng Swagger UI:

1. Truy cập: `http://localhost:8080/swagger-ui.html`
2. Đăng nhập với admin account
3. Tìm section "Data Initialization"
4. Thực hiện các API calls

## Lợi Ích Của Kiến Trúc Mới

### 1. Tách Biệt Trách Nhiệm
- **SystemInitializerConfig**: Chỉ lo khởi tạo dữ liệu hệ thống cơ bản
- **UserManagementService**: Chuyên xử lý logic tạo users
- **DataInitializationController**: Cung cấp API interface

### 2. Linh Hoạt
- Có thể tạo users bất cứ lúc nào thông qua API
- Không cần restart ứng dụng để tạo dữ liệu mẫu
- Có thể tích hợp với CI/CD pipeline

### 3. Bảo Mật
- API yêu cầu authentication và authorization
- Chỉ admin mới có thể tạo dữ liệu mẫu

### 4. Monitoring & Logging
- Tất cả operations đều được log
- API response cung cấp thông tin chi tiết về kết quả

## Migration Notes

**Thay đổi từ phiên bản cũ:**
- `DataInitializer` (CommandLineRunner) → `SystemInitializerConfig` + `UserManagementService`
- Không còn tự động tạo users khi khởi động
- Cần gọi API để tạo users

**Backward Compatibility:**
- Các role, permission vẫn được tạo tự động như trước
- Default credentials không thay đổi
- Kafka integration vẫn hoạt động bình thường
