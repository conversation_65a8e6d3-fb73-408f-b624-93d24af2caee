package com.gianghp.hrm.configs;

import com.gianghp.hrm.security.JwtClaimsFilter;
import java.util.List;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@ConditionalOnProperty(name = "security.config", havingValue = "common")
@EnableWebSecurity
public class CommonSecurityConfig {

  @Bean
  public JwtClaimsFilter jwtClaimsFilter() {
    return new JwtClaimsFilter();
  }

  @Bean
  public SecurityFilterChain commonSecurityFilterChain(HttpSecurity http) throws Exception {
    return http
        .csrf(AbstractHttpConfigurer::disable)
        .authorizeHttpRequests(auth -> auth
            .requestMatchers(ArrayUtils.addAll(new String[]{
                "/v3/api-docs/**",
                "/swagger-ui.html",
                "/swagger-ui/**", "/error",
                "/public/**"
            })).permitAll()
            .anyRequest().authenticated())
        .sessionManagement(session -> session
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .addFilterBefore(jwtClaimsFilter(), UsernamePasswordAuthenticationFilter.class)
        .build();
  }

}
