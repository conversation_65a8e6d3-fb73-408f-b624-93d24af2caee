package com.gianghp.hrm.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

@Component
public class JwtClaimsFilter extends OncePerRequestFilter {

  @Override
  protected void doFilterInternal(HttpServletRequest request,
      HttpServletResponse response,
      FilterChain filterChain) throws ServletException, IOException {

    String referer = request.getHeader("Referer");
    String origin = request.getHeader("Origin");

    boolean fromSwagger =
        (referer != null && referer.contains("/swagger-ui")) ||
            (origin != null && origin.contains("/swagger-ui"));


    // Swagger auto login (dev/test only)
    if (fromSwagger && SecurityContextHolder.getContext().getAuthentication() == null) {

      UserDetails swaggerUser = User.builder()
          .username("swagger-admin")
          .password("")
          .authorities(List.of(() -> "ROLE_ADMIN"))
          .build();

      UsernamePasswordAuthenticationToken authToken =
          new UsernamePasswordAuthenticationToken(swaggerUser, null, swaggerUser.getAuthorities());
      authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

      SecurityContextHolder.getContext().setAuthentication(authToken);
    }

    // Parse user from gateway headers
    if (SecurityContextHolder.getContext().getAuthentication() == null) {
      String username = request.getHeader("X-User-Username");
      String role = request.getHeader("X-User-Role");

      if (username != null) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        if (role != null) {
          authorities.add(new SimpleGrantedAuthority("ROLE_" + role));
        }

        UserDetails userDetails = new User(username, "", authorities);
        UsernamePasswordAuthenticationToken authToken =
            new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authToken);
      }
    }

    filterChain.doFilter(request, response);
  }
}

