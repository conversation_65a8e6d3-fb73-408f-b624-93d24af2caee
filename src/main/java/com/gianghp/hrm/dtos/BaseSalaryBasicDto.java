package com.gianghp.hrm.dtos;

import com.gianghp.hrm.enums.CurrencyCode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseSalaryBasicDto {
  private BigDecimal amount;
  private LocalDate effectiveDate;
}
